import { Signal, useComputed } from "@preact-signals/safe-react";
import { formatTime } from "@repo/lib/utils/time";
import { Progress } from "@repo/ui/components/progress";
import { cn } from "@repo/ui/lib/utils";
import { FC, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { interpolate } from "remotion";
import { useThrottledCallback } from "use-debounce";
import Rectangle from "../assets/Rectangle.svg";

type Size = {
  width: number;
  height: number;
  left: number;
  top: number;
};

// If a pane has been moved, it will cause a layout shift without
// the window having been resized. Those UI elements can call this API to
// force an update
const useElementSize = (
  ref: React.RefObject<HTMLElement | null>
): Size | null => {
  const [size, setSize] = useState<Size | null>(() => {
    if (!ref.current) {
      return null;
    }

    const rect = ref.current.getClientRects();
    if (!rect[0]) {
      return null;
    }

    return {
      width: rect[0].width as number,
      height: rect[0].height as number,
      left: rect[0].x as number,
      top: rect[0].y as number,
    };
  });

  const observer = useMemo(() => {
    if (typeof ResizeObserver === "undefined") {
      return null;
    }

    return new ResizeObserver((entries) => {
      if (!entries[0]) {
        return;
      }
      const { target } = entries[0];
      const newSize = target.getClientRects();

      if (!newSize?.[0]) {
        setSize(null);
        return;
      }

      const { width } = newSize[0];

      const { height } = newSize[0];

      setSize({
        width,
        height,
        left: newSize[0].x,
        top: newSize[0].y,
      });
    });
  }, []);

  const updateSize = useCallback(() => {
    if (!ref.current) {
      return;
    }

    const rect = ref.current.getClientRects();
    if (!rect[0]) {
      setSize(null);
      return;
    }

    setSize((prevState) => {
      if (!rect[0]) {
        return prevState;
      }

      const isSame =
        prevState !== null &&
        prevState.width === rect[0].width &&
        prevState.height === rect[0].height &&
        prevState.left === rect[0].x &&
        prevState.top === rect[0].y;
      if (isSame) {
        return prevState;
      }

      return {
        width: rect[0].width as number,
        height: rect[0].height as number,
        left: rect[0].x as number,
        top: rect[0].y as number,
        windowSize: {
          height: window.innerHeight,
          width: window.innerWidth,
        },
      };
    });
  }, [ref]);

  useEffect(() => {
    if (!observer) {
      return;
    }

    const { current } = ref;
    if (current) {
      observer.observe(current);
    }

    return (): void => {
      if (current) {
        observer.unobserve(current);
      }
    };
  }, [observer, ref, updateSize]);

  useEffect(() => {
    window.addEventListener("resize", updateSize);

    return () => {
      window.removeEventListener("resize", updateSize);
    };
  }, [updateSize]);

  return useMemo(() => {
    if (!size) {
      return null;
    }

    return { ...size, refresh: updateSize };
  }, [size, updateSize]);
};

const calTimeFromX = (clientX: number, duration: number, width: number) => {
  const pos = clientX;

  const time = Math.round(
    interpolate(pos, [0, width], [0, Math.max(duration, 0)], {
      extrapolateLeft: "clamp",
      extrapolateRight: "clamp",
    })
  );
  return time;
};

export const SeekBar: FC<{
  currentTime: Signal<number>;
  duration: Signal<number>;
  seekTo: (time: number) => void;
  className?: string;
  theme?: "dark" | "light";
}> = ({ currentTime, duration, seekTo, className, theme = "light" }) => {
  const [isDragging, _setIsDragging] = useState(false);
  const [dragProgress, setDragProgress] = useState(0); // 拖拽层的进度
  const [dragTime, setDragTime] = useState(0); // 拖拽时的时间
  const isDraggingRef = useRef(false);
  const setIsDragging = useCallback((value: boolean) => {
    _setIsDragging(value);
    isDraggingRef.current = value;
  }, []);
  const ref = useRef<HTMLDivElement>(null);
  const size = useRef({
    width: 0,
    height: 0,
  });

  // 实际播放进度（不受拖拽影响）
  const actualProgress = useComputed(() => {
    return (currentTime.value / duration.value) * 100;
  });

  const time = useComputed(() => {
    return `${formatTime(currentTime.value)} / ${formatTime(duration.value)}`;
  });

  // TODO: 这个SeekTo如果用Throllttled在vod视频播放的时候会出现性能问题，Vod默认在seekTo后会自动播放，
  const throttledSeekTo = useThrottledCallback(seekTo, 150, {
    trailing: true,
  });

  // 统一的拖拽计算逻辑
  const updateDragPosition = useCallback(
    (clientX: number) => {
      const posLeft = ref.current?.getBoundingClientRect().left;
      if (!posLeft) return;

      const dragX = clientX - posLeft;
      const _progress = Math.max(
        0,
        Math.min(100, (dragX / size.current.width) * 100)
      );
      const _time = (_progress / 100) * duration.value;

      setDragProgress(_progress);
      setDragTime(_time);
      throttledSeekTo(_time);
    },
    [duration, throttledSeekTo]
  );

  const onPointerDown = useCallback(
    (e: React.TouchEvent<HTMLDivElement>) => {
      const touch = e.touches[0];
      if (!touch) return;

      setIsDragging(true);
      updateDragPosition(touch.clientX);
    },
    [setIsDragging, updateDragPosition]
  );

  const onMouseDown = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      setIsDragging(true);
      const { clientX } = e;
      if (!ref.current) {
        return;
      }
      const { left } = ref.current.getBoundingClientRect();
      const _time = calTimeFromX(
        clientX - left,
        duration.value,
        size.current.width
      );
      throttledSeekTo(_time);
    },
    [duration, throttledSeekTo, setIsDragging]
  );

  const onMouseMove = useCallback(
    function (e: MouseEvent) {
      if (!isDraggingRef.current) return;
      updateDragPosition(e.clientX);
    },
    [updateDragPosition]
  );

  const [posLeft, setPosLeft] = useState(0);
  useEffect(() => {
    if (!ref.current) {
      return;
    }
    const rect = ref.current.getBoundingClientRect();
    setPosLeft(rect.left);
    size.current.width = rect.width;
  }, [ref]);

  useEffect(() => {
    const body = ref.current;
    if (!body) return;
    const observer = new ResizeObserver(() => {
      const rect = body.getBoundingClientRect();
      setPosLeft(rect.left);
      size.current.width = rect.width;
    });
    observer.observe(body);
    return () => {
      observer.disconnect();
    };
  });

  const onPointerMove = useCallback(
    (e: TouchEvent) => {
      if (!isDraggingRef.current) return;

      const touch = e.touches[0];
      if (!touch) return;

      updateDragPosition(touch.clientX);
    },
    [updateDragPosition]
  );

  const handleDragStop = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    const body = ref.current;
    if (!body) return;

    body.addEventListener("touchend", handleDragStop, { capture: true });
    body.addEventListener("touchmove", onPointerMove, { capture: true });
    body.addEventListener("mouseup", handleDragStop);
    body.addEventListener("mousemove", onMouseMove);
    return () => {
      body.removeEventListener("touchend", handleDragStop);
      body.removeEventListener("touchmove", onPointerMove);
      body.removeEventListener("mouseup", handleDragStop);
      body.removeEventListener("mousemove", onMouseMove);
    };
  }, [handleDragStop, onPointerMove, onMouseMove]);

  return (
    <div
      className={cn(
        "flex h-12 w-full flex-row items-center justify-center gap-4 rounded-xl px-5",
        theme === "dark" ? "bg-[#1F1D1B99]" : "bg-white/80",
        className
      )}
    >
      <div className="relative flex-1 cursor-pointer">
        {/*拖拽时的虚拟层 */}
        {isDragging && (
          <>
            {/* 时间预览气泡 */}
            <div
              className="absolute -top-20 flex flex-col items-center justify-end"
              style={{
                left: `${dragProgress}%`,
                transform: "translateX(-50%)",
              }}
            >
              <div
                className={cn(
                  "flex w-max flex-row items-center justify-center rounded-xl px-4 py-2.5 text-center text-sm font-medium backdrop-blur-sm",
                  "border border-white/20 shadow-lg",
                  theme === "dark"
                    ? "border-white/10 bg-[#1F1D1B]/90 text-[#BDB4AD]"
                    : "border-[#6574FC]/20 bg-[#6574FC]/95 text-white"
                )}
                style={{
                  boxShadow:
                    theme === "dark"
                      ? "0 8px 24px rgba(0, 0, 0, 0.4), 0 4px 8px rgba(0, 0, 0, 0.2)"
                      : "0 8px 24px rgba(101, 116, 252, 0.3), 0 4px 8px rgba(0, 0, 0, 0.1)",
                }}
              >
                {formatTime(dragTime)}
              </div>
              <Rectangle
                className={cn(
                  "mt-1",
                  theme === "dark" ? "fill-[#1F1D1B]/90" : "fill-[#6574FC]/95"
                )}
              />
            </div>

            {/* 拖拽层进度条（虚拟层，跟手） */}
            <div className="absolute inset-0">
              <div
                className={cn(
                  "h-2 w-full rounded-sm",
                  theme === "dark" ? "bg-[#ffffff33]" : "bg-zinc-800/20"
                )}
              >
                {/* 渐变进度条 */}
                <div
                  className={cn(
                    "h-full rounded-sm transition-none",
                    theme === "dark"
                      ? "bg-gradient-to-r from-[#6574FC] to-[#9CB1FC]"
                      : "bg-gradient-to-r from-[#6574FC] to-[#9CB1FC]"
                  )}
                  style={{ width: `${dragProgress}%` }}
                />
                {/* 拖拽按钮 */}
                <div
                  className={cn(
                    "absolute top-1/2 h-6 w-6 -translate-y-1/2 shadow-lg",
                    "flex items-center justify-center"
                  )}
                  style={{
                    left: `${dragProgress}%`,
                    transform: "translateX(-50%) translateY(-50%)",
                    boxShadow:
                      "0 4px 12px rgba(101, 116, 252, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1)",
                  }}
                >
                  {/* 拖拽控制圆点 */}
                  <div
                    className={cn(
                      "h-5 w-5 rounded-full shadow-lg",
                      "border-2 border-white/90",
                      "bg-gradient-to-br from-[#6574FC] to-[#4A4FED]"
                    )}
                    style={{
                      boxShadow:
                        "0 4px 12px rgba(101, 116, 252, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1)",
                    }}
                  />
                </div>
              </div>
            </div>
          </>
        )}

        {/* 实际播放进度条（真实层） */}
        <Progress
          ref={ref}
          onMouseDown={onMouseDown}
          onTouchStart={onPointerDown}
          className={cn(
            "h-2 w-full rounded-sm *:rounded-sm",
            theme === "dark"
              ? "bg-[#ffffff33] *:bg-white"
              : "bg-zinc-800/20 *:bg-zinc-800/90",
            isDragging ? "opacity-30" : "opacity-100"
          )}
          indicatorClassName={cn(
            theme === "dark" ? "bg-white" : "bg-zinc-800/90"
          )}
          value={actualProgress.value}
          max={100}
        />
      </div>
      <div
        className={cn(
          "text-xs font-medium leading-none",
          theme === "dark" ? "text-white/80" : "text-[#33302D]" // 增强对比度：深色主题用白色，浅色主题去掉透明度
        )}
      >
        {time.value}
      </div>
    </div>
  );
};
