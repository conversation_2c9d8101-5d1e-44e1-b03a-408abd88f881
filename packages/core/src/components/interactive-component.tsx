"use client";
import Script from "next/script";
import React, { createElement, memo, useMemo } from "react";

/**
 * 互动组件
 * @param url 互动组件文件URL（支持.js和.html文件）
 * @param type 互动组件的类型
 * @param children 可传递在组件加载过程中的 loading UI
 * @param onReport 互动组件的上报事件
 * @param onLoad 组件加载完成回调
 * @param onError 组件加载错误回调
 *
 * 支持的文件类型：
 * - .js文件：使用Script标签加载，支持自定义元素
 * - .html文件：使用iframe渲染，支持完整的HTML页面
 */
function InteractiveComponent<
  P extends React.HtmlHTMLAttributes<T>,
  T extends Element,
>({
  url,
  type,
  children = null,
  onLoad,
  onError,
  ...props
}: React.ClassAttributes<T> &
  P & {
    url: string;
    type: string;
    onReport?: (e: CustomEvent) => void;
    onLoad?: () => void;
    onError?: (e: unknown) => void;
    children?: React.ReactNode;
  }) {
  const dom = useMemo(() => {
    const result = createElement(
      type,
      {
        ...props,
        ref: (element: Element | null) => {
          if (element && element.shadowRoot) {
            const style = document.createElement("style");
            style.textContent = `
            :host{height: 100%;overflow-y: auto;}
            .main-container{min-height: 100%;}
            `;
            element.shadowRoot.appendChild(style);
          }
        },
      },
      ...(Array.isArray(children) ? children : [children])
    );
    return result;
  }, [type, props, children]);
  // 检测文件类型
  const isHtmlFile = url.toLowerCase().endsWith(".html");

  return (
    <>
      {isHtmlFile ? (
        // HTML文件使用iframe渲染
        <iframe
          src={url}
          className="h-full w-full border-0"
          title="HTML互动组件"
          sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
          onLoad={onLoad}
          onError={onError}
          style={{
            minHeight: "100%",
            backgroundColor: "transparent",
          }}
        />
      ) : (
        // JS文件使用原有逻辑
        <>
          <Script src={url} onLoad={onLoad} onError={onError} />
          {dom}
        </>
      )}
    </>
  );
}

export default memo(InteractiveComponent);

// 使用示例：
// function Demo() {
//   return (
//     <InteractiveComponent
//       url="https://www.baidu.com/a/b/c"
//       type="aab-ddc"
//       onClick={(e) => {}}
//       onAAA={(e) => {}}
//     >
//       {/* 这里的内容会在组件加载期间显示 */}
//       <div className="loading-spinner">加载互动组件中...</div>
//     </InteractiveComponent>
//   );
// }
//
// HTML文件使用示例：
// function HtmlDemo() {
//   return (
//     <InteractiveComponent
//       url="https://example.com/interactive.html"
//       type="html-interactive" // HTML文件时type参数会被忽略
//       onLoad={() => console.log('HTML组件加载完成')}
//       onError={(e) => console.error('HTML组件加载失败', e)}
//     >
//       <div className="loading-spinner">加载HTML组件中...</div>
//     </InteractiveComponent>
//   );
// }
