export type WebTokenRequest = {
  resourceId: string;
};

export type WebTokenResponse = {
  headers: {
    "Content-Type": string;
  };
  statusCode: number;
  body: WebTokenInfo;
};

export type WebTokenInfo = {
  accessToken: string;
  accessTokenExpiredTime: string;
  webofficeURL: string;
  refreshToken: string;
  refreshTokenExpiredTime: string;
  requestId?: string;
};

export type RefreshTokenRequest = {
  AccessToken: string;
  RefreshToken: string;
};
export type RefreshTokenResponse = Omit<WebTokenResponse["body"], "previewUrl">;

export type GenerateTokenMethod = (
  params?: WebTokenRequest
) => Promise<WebTokenInfo>;

export type RefreshTokenMethod = (
  params: RefreshTokenRequest
) => Promise<RefreshTokenResponse>;

export type IMMInstance = {
  setToken: (params: { token: string; timeout?: number }) => void;
  ready: () => Promise<void>;
  Application: {
    ActiveDocument: {
      SwitchTypoMode: (mode: boolean) => Promise<boolean>;
    };
    ActivePresentation: {
      SlideShowSettings: {
        SetMiniThumbnailVisible: (visible: boolean) => Promise<boolean>;
      };
      SlideShowWindow: {
        View: {
          GotoSlide: (slideIndex: number) => Promise<void>;
        };
      };
    };
    ActivePDF: {
      PageMode: number;
    };
  };
  iframe: HTMLIFrameElement;
  destroy: () => void;
  // 可参考文档1：https://help.aliyun.com/zh/imm/user-guide/events-1
  // 可参考文档2：https://help.aliyun.com/zh/imm/user-guide/event-handling
  /** @deprecated */
  on: (event: string, callback: (...args: unknown[]) => void) => void;
  /** @deprecated */
  off: (event: string, callback: (...args: unknown[]) => void) => void;
  ApiEvent: {
    AddApiEventListener: (
      event: string,
      callback: (...args: unknown[]) => void
    ) => void;
    RemoveApiEventListener: (
      event: string,
      callback: (...args: unknown[]) => void
    ) => void;
  };
};

declare global {
  interface Window {
    aliyun?: {
      config: (config: {
        url: string;
        mount?: HTMLElement;
        // 普通模式，展示所有功能界面；极简模式，不显示头部和工具栏。
        mode?: "normal" | "simple";

        refreshToken?: () => Promise<{ token: string; timeout: number }>;

        // 外链跳转
        onHyperLinkOpen?: ({ linkUrl }: { linkUrl: string }) => boolean; // 也可以返回void，但type里卡死下，避免bug

        // TODO: 待补充更多的参数：https://help.aliyun.com/zh/imm/user-guide/parameters
      }) => IMMInstance;
    };
  }
}
