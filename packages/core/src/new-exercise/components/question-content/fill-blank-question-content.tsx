import { FormatMath } from "@repo/core/new-exercise/components";
import { useSelfEvaluation } from "@repo/core/new-exercise/hooks/use-self-evaluation";
import { parseContent } from "@repo/core/new-exercise/utils/parseFillBlank";
import HalfRightIconRedSvg from "@repo/core/public/assets/stu-exercise/icons/fill-blank/half-right-red.svg";
import RightIconGreenSvg from "@repo/core/public/assets/stu-exercise/icons/fill-blank/right-green.svg";
import WrongIconRedSvg from "@repo/core/public/assets/stu-exercise/icons/fill-blank/wrong-red.svg";
import { cn } from "@repo/ui/lib/utils";
import { useCallback, useEffect } from "react";
import { InputModeType, SelfEvaluateType } from "../../enums";
import { QuestionContentProps } from "./types";
import {
  convertParagraphToHtml,
  getEvalResult,
  isTableContent,
  resolveBlankClasses,
  resolveNumberIndicatorClasses,
} from "./utils";

/** 非表格时的结果图标 JSX */
const renderEvalIcon = (evalResult: SelfEvaluateType) => {
  if (!evalResult) return null;
  if (evalResult === SelfEvaluateType.right) {
    return (
      <RightIconGreenSvg className="ml-1 inline-block h-4 w-4 align-middle" />
    );
  }
  if (evalResult === SelfEvaluateType.partial) {
    return (
      <HalfRightIconRedSvg className="ml-1 inline-block h-4 w-4 align-middle" />
    );
  }
  return <WrongIconRedSvg className="ml-1 inline-block h-4 w-4 align-middle" />;
};

/** 获取SVG图标的HTML字符串 */
const getSvgIcon = (evalResult: SelfEvaluateType): string => {
  if (evalResult === SelfEvaluateType.right) {
    return `<svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill="#10B981"/>
    </svg>`;
  }
  if (evalResult === SelfEvaluateType.partial) {
    return `<svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="#F59E0B"/>
    </svg>`;
  }
  return `<svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="#EF4444"/>
  </svg>`;
};

/** BlankReplacer 组件：用于将占位符替换为实际的空白组件 */
interface BlankReplacerProps {
  paragraphIndex: number;
  onBlankClick: (index: number) => void;
  getAnswerValue: (index: number) => string;
  isCompleted: boolean;
  selfEvaluation: SelfEvaluateType[] | null;
  activeBlankIndex: number;
  totalBlanks: number;
  hasSingleBlank: boolean;
  type?: string;
}

const BlankReplacer: React.FC<BlankReplacerProps> = ({
  paragraphIndex,
  onBlankClick,
  getAnswerValue,
  isCompleted,
  selfEvaluation,
  activeBlankIndex,
  totalBlanks,
  hasSingleBlank,
  type,
}) => {
  // 🔧 提取公共的空白元素构建逻辑
  const buildBlankElement = useCallback(
    (blankIndex: number, element?: Element): HTMLSpanElement => {
      const evalResult = getEvalResult(
        blankIndex,
        isCompleted,
        selfEvaluation || []
      );

      const isActive = activeBlankIndex === blankIndex;
      const {
        text: colorClass,
        border: borderClass,
        bg: bgClass,
      } = resolveBlankClasses({
        evalResult,
        isActive,
        isCompleted,
        highlightWithLiteBg: Boolean(type),
      });

      const answerValue = getAnswerValue(blankIndex);
      const hasValue = Boolean(answerValue);

      // 创建或复用空白元素
      const blankElement =
        (element as HTMLSpanElement) || document.createElement("span");

      // 设置基础属性和样式
      blankElement.className = cn(
        "fill_blank_input_area border-b-1 relative mx-1 inline-flex h-8 w-[10.625rem] cursor-pointer items-center justify-center align-top",
        borderClass,
        colorClass,
        bgClass || ""
      );
      blankElement.setAttribute("data-blank-index", blankIndex.toString());
      blankElement.setAttribute(
        "data-paragraph-index",
        paragraphIndex.toString()
      );

      // 清空内容（对于更新场景）
      blankElement.innerHTML = "";

      // 🔧 统一的序号显示逻辑：母子题不显示序号
      const shouldShowNumber = !hasSingleBlank && type !== "child_parent";
      if (shouldShowNumber) {
        const numberSpan = document.createElement("span");
        numberSpan.className = cn(
          resolveNumberIndicatorClasses({
            evalResult,
            isActive,
            isCompleted,
            hasValue,
            colorClass,
          }),
          hasValue ? "ml-3" : ""
        );
        numberSpan.textContent = (blankIndex + 1).toString();
        blankElement.appendChild(numberSpan);
      }

      // 添加答案文本（如果有）
      if (hasValue) {
        const answerSpan = document.createElement("span");
        answerSpan.className = cn(
          "fill_blank_answer_text flex-1 truncate px-1",
          hasSingleBlank ? "" : "ml-2",
          colorClass
        );
        answerSpan.textContent = answerValue;
        blankElement.appendChild(answerSpan);
      }

      // 添加评价图标（如果需要）
      if (isCompleted && evalResult) {
        const iconSpan = document.createElement("span");
        iconSpan.className =
          "fill_blank_evaluation_icon mr-3 flex items-center";

        const iconElement = document.createElement("div");
        iconElement.className = "ml-1 inline-block h-4 w-4 align-middle";
        iconElement.innerHTML = getSvgIcon(evalResult);
        iconSpan.appendChild(iconElement);
        blankElement.appendChild(iconSpan);
      }

      return blankElement;
    },
    [
      paragraphIndex,
      isCompleted,
      selfEvaluation,
      activeBlankIndex,
      getAnswerValue,
      hasSingleBlank,
      type,
    ]
  );

  // 初始化：替换占位符为空白元素（只执行一次）
  useEffect(() => {
    const timer = setTimeout(() => {
      const placeholders = document.querySelectorAll(
        `[data-blank-placeholder^="${paragraphIndex}-"]`
      );

      if (placeholders.length === 0) {
        console.log(
          `No placeholders found for paragraph ${paragraphIndex}, retrying...`
        );
        return;
      }

      placeholders.forEach((placeholder) => {
        const indexStr = placeholder
          .getAttribute("data-blank-placeholder")
          ?.split("-")[1];
        if (!indexStr) return;

        const blankIndex = parseInt(indexStr, 10);
        const blankElement = buildBlankElement(blankIndex);

        // 添加点击事件（只在初始化时添加）
        blankElement.addEventListener("click", (e) => {
          e.preventDefault();
          e.stopPropagation();
          onBlankClick(blankIndex);
        });

        // 替换占位符
        placeholder.replaceWith(blankElement);
      });
    }, 100);

    return () => clearTimeout(timer);
  }, [paragraphIndex, onBlankClick, buildBlankElement]);

  // 🔧 更新样式和内容：当状态变化时更新所有空白元素
  useEffect(() => {
    const blankElements = document.querySelectorAll(
      `[data-paragraph-index="${paragraphIndex}"][data-blank-index]`
    );

    blankElements.forEach((element) => {
      const blankIndexStr = element.getAttribute("data-blank-index");
      if (!blankIndexStr) return;

      const blankIndex = parseInt(blankIndexStr, 10);

      // 🔧 使用统一的构建逻辑，复用现有元素
      buildBlankElement(blankIndex, element);
    });
  }, [buildBlankElement, paragraphIndex]); // 🔧 简化依赖，buildBlankElement 已经包含了所有必要的依赖

  return null; // 这个组件不渲染任何内容，只是执行副作用
};

/**
 * 填空题内容渲染组件
 */
const FillBlankQuestionContent = ({
  activeBlankIndex,
  onBlankClick,
  isCompleted = false,
  questionStem,
  questionId,
  type = "",
  answers,
  inputMode,
}: QuestionContentProps & {
  isCompleted: boolean;
  selfEvaluation?: SelfEvaluateType[];
}) => {
  const { selfEvaluation } = useSelfEvaluation();
  const paragraphs = parseContent(questionStem || "");

  const totalBlanks = answers.length;
  const hasSingleBlank = totalBlanks === 1;
  const isHideValue =
    inputMode === InputModeType.CAMERA ||
    inputMode === InputModeType.WHITEBOARD;

  const getAnswerValue = useCallback(
    (i: number): string => {
      const arr = answers ?? [];
      return isHideValue ? "" : (arr[i]?.content ?? "");
    },
    [isHideValue, answers]
  );

  // 设置表格内点击（保持原行为）
  useEffect(() => {
    (window as any).handleBlankClick = onBlankClick;
    return () => {
      delete (window as any).handleBlankClick;
    };
  }, [onBlankClick]);

  // 行内递增空索引
  let blankIdx = 0;

  return (
    <div className="fill_blank_question_content_container w-full">
      <div className="fill_blank_content_wrapper flex flex-col gap-4">
        <div className="fill_blank_text_content text-text-1 text-[1.0625rem] leading-8">
          {paragraphs.map((parts, pIdx) => {
            if (pIdx === 0) blankIdx = 0;

            if (isTableContent(parts)) {
              const tableHtml = convertParagraphToHtml({
                parts,
                getAnswerValue,
                getEval: (i) =>
                  getEvalResult(i, isCompleted, selfEvaluation.value) ??
                  SelfEvaluateType.wrong,
                activeBlankIndex,
                isCompleted,
                hasSingleBlank,
                type,
              });

              return (
                <div key={pIdx} className="fill_blank_table_container mb-5">
                  <FormatMath
                    htmlContent={tableHtml}
                    questionId={questionId || ""}
                  />
                </div>
              );
            }

            // 普通段落 - 将所有内容放在一个FormatMath组件中
            // 使用唯一占位符标记空白位置
            const BLANK_PLACEHOLDER = `<span data-blank-placeholder="${pIdx}-{INDEX}"></span>`;
            let mergedHtml = "";
            let currentBlankIndex = blankIdx;

            // 构建合并的HTML字符串，空白处用占位符替换
            parts.forEach((part) => {
              if (part === null) {
                mergedHtml += BLANK_PLACEHOLDER.replace(
                  "{INDEX}",
                  currentBlankIndex.toString()
                );
                currentBlankIndex++;
              } else {
                mergedHtml += part;
              }
            });

            // 更新全局空白索引
            blankIdx = currentBlankIndex;

            return (
              <div
                key={pIdx}
                className="fill_blank_paragraph mb-5 leading-[190%]"
              >
                <FormatMath
                  htmlContent={mergedHtml}
                  questionId={questionId || ""}
                  className="inline-block"
                  imageProcess={{
                    enableLayout: true,
                    enablePreview: true,
                    imagesPerRow: 2,
                    imageMargin: "10px",
                  }}
                  // 添加标记，表示这是填空题使用
                  fillBlankMode={true}
                />
                <BlankReplacer
                  paragraphIndex={pIdx}
                  onBlankClick={onBlankClick}
                  getAnswerValue={getAnswerValue}
                  isCompleted={isCompleted}
                  selfEvaluation={selfEvaluation.value}
                  activeBlankIndex={activeBlankIndex}
                  totalBlanks={totalBlanks}
                  hasSingleBlank={hasSingleBlank}
                  type={type}
                />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default FillBlankQuestionContent;
