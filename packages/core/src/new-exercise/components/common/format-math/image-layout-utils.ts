/**
 * 图片排版工具函数 - 简化版
 * 处理连续图片的智能排版，使用 Grid 布局
 */

// 图片基准尺寸常量
const BASE_IMAGE_DIMENSIONS = {
  width: 25.125, // rem
  height: 16.75, // rem
} as const;

/**
 * 检查图片是否已经被包装过
 */
function isImageAlreadyWrapped(imgTag: string): boolean {
  return (
    imgTag.includes("image-with-preview") ||
    imgTag.includes("image-preview-btn")
  );
}

/**
 * 提取图片src地址
 */
function extractImageSrc(imgTag: string): string {
  const srcMatch = imgTag.match(/src="([^"]*)"/);
  return srcMatch ? (srcMatch[1] ?? "") : "";
}

/**
 * 为图片添加预览按钮包装
 */
function wrapImageWithPreviewButton(
  imgTag: string,
  imageSrc: string,
  enablePreview: boolean = true
): string {
  if (isImageAlreadyWrapped(imgTag)) {
    return imgTag;
  }

  // 如果不启用预览，只返回基本的图片标签，不添加预览包装
  if (!enablePreview) {
    return `
      <div class="image-with-preview" style="
        position: relative;
        display: block;
        width: 100%;
        height: 100%;
      ">
        <img src="${imageSrc}" style="
          /* width: 100%; */
          max-width: 100%;
          max-height: 100%;
        " />
      </div>
    `;
  }

  // 启用预览时，添加预览包装
  return `
    <div class="image-with-preview image-preview-wrapper" style="
      position: relative;
      display: block;
      width: 100%;
      height: 100%;
    ">
      <img src="${imageSrc}" style="
        /* width: 100%; */
        max-width: 100%;
        max-height: 100%;
      " />
    </div>
  `;
}

/**
 * 处理连续图片组，使用 Grid 布局
 */
function formatImageGroup(
  images: string[],
  imagesPerRow: number = 2,
  enablePreview: boolean = true,
  imageMargin: string = "0px"
): string {
  if (images.length === 0) return "";

  // 计算容器和单个图片的尺寸
  let containerWidth: string;
  let itemHeight: string;

  if (images.length === 1) {
    // 单张图片使用基准尺寸
    containerWidth = `${BASE_IMAGE_DIMENSIONS.width}rem`;
    // itemHeight = `${BASE_IMAGE_DIMENSIONS.height}rem`;
    itemHeight = "auto";
    imagesPerRow = 1;
  } else {
    // 多张图片时
    containerWidth = `${BASE_IMAGE_DIMENSIONS.width}rem`;
    itemHeight = `${BASE_IMAGE_DIMENSIONS.height / 2}rem`; // 高度减半
  }

  // 处理图片，添加预览功能
  const processedImages = images.map((imgTag) => {
    const imageSrc = extractImageSrc(imgTag);
    return wrapImageWithPreviewButton(imgTag, imageSrc, enablePreview);
  });

  // 使用 Grid 布局
  return `
    <div class="formatted-image-group image-layout-container" style="
      /* width: ${containerWidth}; */
      display: grid;
      grid-template-columns: repeat(${imagesPerRow}, 1fr);
      gap: 12px;
      padding: 0.01px;
      margin-top: ${imageMargin};
    ">
      ${processedImages
        .map(
          (img) => `
        <div
         class="image-layout-item"
         style="
          height: ${itemHeight};
          max-height: 10rem;
          position: relative;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          ${img}
        </div>
      `
        )
        .join("")}
    </div>
  `;
}

/**
 * 主要处理函数：匹配连续图片并应用布局
 */
export function processConsecutiveImages(
  htmlContent: string,
  imagesPerRow: number = 2,
  enablePreview: boolean = true,
  imageMargin: string = "0px"
): string {
  // 匹配连续图片（包括单张）
  return htmlContent.replace(/(<img[^>]*>(\s*<img[^>]*>)*)/g, (match) => {
    const imgTags = match.match(/<img[^>]*>/g) || [];
    if (imgTags.length > 0) {
      return formatImageGroup(
        imgTags,
        imagesPerRow,
        enablePreview,
        imageMargin
      );
    }
    return match;
  });
}

/**
 * 主要导出函数
 */
export function applyImageLayout(
  htmlContent: string,
  imagesPerRow: number = 2,
  enablePreview: boolean = true
): string {
  return processConsecutiveImages(htmlContent, imagesPerRow, enablePreview);
}

/**
 * 根据配置应用图片处理功能
 */
export function applyImageProcessing(
  htmlContent: string,
  enableLayout: boolean = true,
  enablePreview: boolean = true,
  imagesPerRow: number = 2,
  imageMargin: string = "0px"
): string {
  if (!enableLayout && !enablePreview) {
    return htmlContent;
  }

  if (enableLayout) {
    return processConsecutiveImages(
      htmlContent,
      imagesPerRow,
      enablePreview,
      imageMargin
    );
  }

  return htmlContent;
}
