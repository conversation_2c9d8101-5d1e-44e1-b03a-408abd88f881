"use client";

import { CommonQuestionAnswerOption } from "@repo/core/types";
import { cn } from "@repo/ui/lib/utils";
import React, {
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { QuestionRenderType } from "../../../enums";
import { FormatMath } from "../../common/format-math";
import { Checkbox } from "./components/checkbox";
import CorrectAnswerAnimation from "./components/CorrectAnswerAnimation";
import StateIndicator from "./components/StateIndicator";

export interface ChoiceOption {
  id: string;
  key: string;
  content: string;
  isCorrect?: boolean;
}

// 选项状态枚举
export enum OptionState {
  NORMAL = "normal",
  SELECTED = "selected",
  CORRECT = "correct",
  INCORRECT = "incorrect",
  MISSED = "missed",
}

// 判断题选项文本显示组件
interface JudgmentOptionTextProps {
  optionKey: string;
  isEnglish: boolean;
  className?: string;
}

const JudgmentOptionText: React.FC<JudgmentOptionTextProps> = ({
  optionKey,
  isEnglish,
  className,
}) => {
  const isTrue = optionKey.toLowerCase() === "true";

  const text = isEnglish
    ? isTrue
      ? "TRUE"
      : "FALSE"
    : isTrue
      ? "正确"
      : "错误";

  return <div className={className}>{text}</div>;
};

interface ChoiceOptionsListProps {
  questionId: string;
  options: ChoiceOption[];
  selectedOptions: string[];
  onSelectOption: (optionId: string) => void;
  correctAnswer?: CommonQuestionAnswerOption[];
  showResult?: boolean;
  questionRenderType?: QuestionRenderType;
  answerStat?: Array<{
    optionKey: string;
    rate: string; // 🔥 统一使用 string 类型
  }>; // 🔥 外部传入第一个 answerStat，选择题相当于有一个空
  isPreview?: boolean;
  isEnglish?: boolean;
}

// ✨ 新增：用于测量 KaTeX/HTML 的渲染宽度
import katex from "katex";
import "katex/dist/katex.min.css";
// ======= 布局测量相关常量（与 DOM/Tailwind 对齐） =======
const GRID_GAP_PX = 12; // gap-3 => 12px
const BUTTON_PADDING_X_PX = 24; // px-6 => 24px
const INNER_GAP_PX = 12; // .option-text-content 内部 gap 近似值
const KEY_MIN_WIDTH_PX = 20; // 选项序号最小占位（如 "A.")

// ✨ KaTeX/HTML 渲染到字符串（行内/块级都覆盖）
const renderMathHtml = (html: string): string => {
  if (!html) return "";
  let processed = html;
  try {
    const inlineParen = /\\\((.*?)\\\)/g;
    const inlineDollar = /\$(.*?)\$/g;
    const blockParen = /\\\[([\s\S]*?[^\\])\\\]/g;
    const blockDollar = /\$\$(.*?)\$\$/g;

    processed = processed.replace(blockParen, (_, f) =>
      katex.renderToString(f, { throwOnError: false, displayMode: true })
    );
    processed = processed.replace(blockDollar, (_, f) =>
      katex.renderToString(f, { throwOnError: false, displayMode: true })
    );
    processed = processed.replace(inlineParen, (_, f) =>
      katex.renderToString(f, { throwOnError: false, displayMode: false })
    );
    processed = processed.replace(inlineDollar, (_, f) =>
      katex.renderToString(f, { throwOnError: false, displayMode: false })
    );
  } catch (e) {
    // 静默失败，使用原始 html
  }
  return processed;
};

// ✨ 宽度缓存，避免重复测量
const widthCache = new Map<string, number>();
// ✨ 缩放比例缓存，避免重复计算
const scaleCache = new Map<string, number>();

// 局部测量函数：渲染为 KaTeX 后，取 .katex 的最大 offsetWidth（忽略图片与普通文本）
const getMaxKaTeXWidthLocal = (rawHtml: string): number => {
  if (typeof document === "undefined") return 0;
  if (widthCache.has(rawHtml)) return widthCache.get(rawHtml)!;
  const processed = renderMathHtml(rawHtml);
  const temp = document.createElement("div");
  temp.style.position = "absolute";
  temp.style.left = "-99999px";
  temp.style.top = "-99999px";
  temp.style.visibility = "hidden";
  temp.style.pointerEvents = "none";
  temp.style.whiteSpace = "normal";
  temp.style.lineHeight = "1.9";
  temp.style.fontSize = "16px";
  temp.style.fontWeight = "500";
  temp.style.fontFamily =
    '"Alibaba PuHuiTi 3.0", system-ui, -apple-system, Segoe UI, Roboto, sans-serif';

  const htmlNoImg = processed.replace(/<img/gi, "<x-img");
  temp.innerHTML = `<div class="format-math-measure">${htmlNoImg}</div>`;
  document.body.appendChild(temp);

  const nodes = temp.querySelectorAll(".katex");
  let maxW = 0;
  nodes.forEach((n) => {
    const w = (n as HTMLElement).offsetWidth;
    if (w > maxW) maxW = w;
  });

  document.body.removeChild(temp);
  widthCache.set(rawHtml, maxW);
  return maxW;
};

// ✨ 新增：计算公式缩放比例
const calculateFormulaScale = (
  rawHtml: string,
  availableWidth: number,
  minScale: number = 0.4
): number => {
  if (typeof document === "undefined") return 1;

  const cacheKey = `${rawHtml}-${availableWidth}`;
  if (scaleCache.has(cacheKey)) return scaleCache.get(cacheKey)!;

  const formulaWidth = getMaxKaTeXWidthLocal(rawHtml);
  if (formulaWidth <= 0 || availableWidth <= 0) {
    scaleCache.set(cacheKey, 1);
    return 1;
  }

  // ✨ 修复：只有当公式宽度超出可用宽度时才缩放
  if (formulaWidth <= availableWidth) {
    scaleCache.set(cacheKey, 1);
    return 1;
  }

  const scale = Math.max(minScale, availableWidth / formulaWidth);
  scaleCache.set(cacheKey, scale);
  return scale;
};

// 主组件
export const ChoiceOptionsList: React.FC<ChoiceOptionsListProps> = ({
  options,
  selectedOptions,
  onSelectOption,
  correctAnswer,
  showResult = false,
  questionRenderType = QuestionRenderType.SINGLE_CHOICE,
  questionId,
  isPreview = false,
  answerStat = [],
  isEnglish = false,
}) => {
  // 状态管理
  const [hasImages, setHasImages] = useState(false);
  const [animatingOptions, setAnimatingOptions] = useState<Set<string>>(
    new Set()
  );

  // ======= 新增：布局状态与容器 ref =======
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [layoutClass, setLayoutClass] = useState<string>(
    "flex flex-col gap-y-4"
  );
  const [layoutType, setLayoutType] = useState<number>(0); // 0: 单列, 1: 双列
  const [isLayoutReady, setIsLayoutReady] = useState<boolean>(false);
  // 公式缩放状态管理
  const [formulaScales, setFormulaScales] = useState<Map<string, number>>(
    new Map()
  );

  // 计算正确答案
  const correctAnswerKeys = useMemo(() => {
    if (!correctAnswer) return [];
    return correctAnswer
      .map((answer) => answer.optionKey)
      .filter(Boolean) as string[];
  }, [correctAnswer]);

  // 正确答案的选项映射
  const correctAnswerMap = useMemo(() => {
    const map = new Map<string, boolean>();
    correctAnswerKeys.forEach((key) => map.set(key, true));
    return map;
  }, [correctAnswerKeys]);

  // 是否选择了选项
  const hasSelectedOptions = useMemo(
    () => selectedOptions.length > 0,
    [selectedOptions]
  );

  // 已选择的是否包含错误，对比正确答案和已选择选项
  const hasIncorrect = useMemo(() => {
    if (!hasSelectedOptions) return true;
    return !selectedOptions.every((key) => correctAnswerMap.has(key));
  }, [correctAnswerMap, hasSelectedOptions, selectedOptions]);

  const isMultipleChoiceQuestion = useMemo(() => {
    return questionRenderType === QuestionRenderType.MULTIPLE_CHOICE;
  }, [questionRenderType]);

  // 是否为判断题
  const isJudgmentQuestion = useMemo(() => {
    return questionRenderType === QuestionRenderType.JUDGMENT;
  }, [questionRenderType]);

  // 检测是否包含图片
  useEffect(() => {
    const containsImages = options.some(
      (option) => !!option.content.includes("<img")
    );
    setHasImages(containsImages);
  }, [options]);

  // 重置动画状态
  useEffect(() => {
    setAnimatingOptions(new Set());
  }, [options]);

  // 获取选项状态
  const getOptionState = useCallback(
    (optionId: string, isSelected: boolean): OptionState => {
      const isCorrectAnswer = correctAnswerKeys.includes(optionId);

      if (!showResult) {
        return isSelected ? OptionState.SELECTED : OptionState.NORMAL;
      }
      // 选中且正确或者单选题且正确
      if (
        (isSelected && isCorrectAnswer) ||
        (questionRenderType === QuestionRenderType.SINGLE_CHOICE &&
          isCorrectAnswer)
      )
        return OptionState.CORRECT;
      // 选中且错误
      if (isSelected && !isCorrectAnswer) return OptionState.INCORRECT;
      // 未选中且正确
      if (!isSelected && isCorrectAnswer) return OptionState.MISSED;

      return OptionState.NORMAL;
    },
    [showResult, correctAnswerKeys, questionRenderType]
  );

  // 管理动画状态
  useEffect(() => {
    if (!showResult) return;

    const newAnimatingOptions = new Set<string>();
    options.forEach((option) => {
      const state = getOptionState(
        option.id,
        selectedOptions.includes(option.id)
      );
      if (
        state === OptionState.CORRECT &&
        selectedOptions.includes(option.id)
      ) {
        newAnimatingOptions.add(option.id);
      }
    });

    if (newAnimatingOptions.size > 0) {
      setAnimatingOptions(newAnimatingOptions);
      const timer = setTimeout(() => setAnimatingOptions(new Set()), 3000);
      return () => clearTimeout(timer);
    }
  }, [showResult, selectedOptions, options, getOptionState]);

  // 🔥 统一的选项样式管理函数
  const getOptionStyles = useCallback(
    (state: OptionState) => {
      const baseClasses =
        "w-full py-4 px-6 transition-all relative text-[1.0625rem]";
      const normalClasses =
        "bg-[rgba(255,255,255,0.60)] rounded-[0.75rem] border border-[1px] border-[#fff] outline outline-[1px] outline-[#fff] shadow-[0px_1px_4px_0px_rgba(64,43,26,0.05)]";

      // 样式配置映射
      const styleConfig = {
        [OptionState.NORMAL]: {
          className: normalClasses,
          style: showResult ? { backgroundColor: "#fff" } : {},
          textColor: showResult ? "text-text-5" : "text-text-2",
        },
        [OptionState.SELECTED]: {
          className: "rounded-[0.75rem] border border-[rgba(88,196,250,0.60)]",
          style: { background: "#EAF8FF" },
          textColor: "text-text-2",
        },
        [OptionState.CORRECT]: {
          className: "rounded-[0.75rem] border border-[rgba(132,214,75,0.60)]",
          style: { background: "#E5FFD1" },
          textColor: "text-text-2",
        },
        [OptionState.INCORRECT]: {
          className:
            "rounded-[0.75rem] border border-[rgba(255,123,89,0.60)] shadow-[0px_4px_20px_0px_rgba(35,42,64,0.02)]",
          style: { background: "#FFEAE4" },
          textColor: "text-text-2",
        },
        [OptionState.MISSED]: {
          className: normalClasses,
          style: {},
          textColor: "text-text-2",
        },
      };

      const config = styleConfig[state];

      return {
        className: `${baseClasses} ${config.className}`,
        style: config.style,
        textColorClass: config.textColor,
      };
    },
    [showResult]
  );

  // 判断是否应该显示百分比（至少有一个选项的 rate 不为 0）
  const shouldShowPercentage = useMemo(() => {
    if (!answerStat || answerStat.length === 0) return false;

    return answerStat.some((item) => {
      const rateNumber = parseFloat(item.rate || "0");
      return rateNumber > 0;
    });
  }, [answerStat]);

  // 获取选项百分比
  const getOptionPercentage = useCallback(
    (optionKey: string): string => {
      // 🔧 如果不应该显示百分比，返回空字符串
      if (!shouldShowPercentage) return "";

      if (!answerStat || answerStat.length === 0) return "0%";

      const stat = answerStat.find((item) => item.optionKey === optionKey);
      if (!stat) return "0%";

      // 🔥 将 string 类型的 rate 转换为 number
      const rateNumber = parseFloat(stat.rate);
      const percentage = (rateNumber * 100).toFixed(1);
      return `${percentage}%`;
    },
    [answerStat, shouldShowPercentage]
  );

  const handleOptionClick = useCallback(
    (optionId: string) => {
      if (showResult) return;
      onSelectOption(optionId);
    },
    [showResult, onSelectOption]
  );

  // 派生选项列表，避免修改 props
  const optionList = useMemo(
    () =>
      options.length > 0
        ? options
        : [{ id: "empty", key: "Invalid", content: "暂无选项数据" }],
    [options]
  );

  // ======= 核心：两列虚拟 DOM 测量并自动回退一列 =======
  const measureAndDecideLayout = useCallback(() => {
    const wrap = containerRef.current;
    if (!wrap) return;

    const containerWidth = wrap.clientWidth;
    if (containerWidth <= 0) return;

    const anyImage = optionList.some((opt) => opt.content.includes("<img"));
    const anyMath = optionList.some(
      (opt) =>
        opt.content.includes("katex") ||
        opt.content.includes("\\(") ||
        opt.content.includes("\\[") ||
        /\$/.test(opt.content)
    );

    // 单列时计算公式缩放比例
    const optionListWithScales = () => {
      const singleColAvailableWidth =
        containerWidth -
        2 * BUTTON_PADDING_X_PX -
        KEY_MIN_WIDTH_PX -
        INNER_GAP_PX;
      const newScales = new Map<string, number>();

      optionList.forEach((opt) => {
        if (
          opt.content.includes("katex") ||
          opt.content.includes("\\(") ||
          opt.content.includes("\\[") ||
          /\$/.test(opt.content)
        ) {
          const scale = calculateFormulaScale(
            opt.content,
            singleColAvailableWidth
          );
          newScales.set(opt.id, scale);
        }
      });

      return newScales;
    };

    // 没有图片 => 单列
    if (!anyImage) {
      setLayoutClass("flex flex-col gap-y-4");
      setLayoutType(0);

      // 有公式 => 单列时计算公式缩放比例
      if (anyMath) {
        setFormulaScales(optionListWithScales);
      } else {
        setFormulaScales(new Map());
      }

      setIsLayoutReady(true);
      return;
    }

    // 有图片但没有公式 => 直接双列
    if (anyImage && !anyMath) {
      setLayoutClass("grid grid-cols-2 gap-3");
      setLayoutType(1);
      setFormulaScales(new Map()); // 双列时不需要缩放
      setIsLayoutReady(true);
      return;
    }

    // 有图片且有公式 => 仅测"公式"的最大宽度，判断两列是否可放下
    const colWidth = Math.floor((containerWidth - GRID_GAP_PX) / 2);
    const contentAvailable =
      colWidth - 2 * BUTTON_PADDING_X_PX - KEY_MIN_WIDTH_PX - INNER_GAP_PX;

    const existsWideFormula = optionList.some((opt) => {
      if (
        !(
          opt.content.includes("katex") ||
          opt.content.includes("\\(") ||
          opt.content.includes("\\[") ||
          /\$/.test(opt.content)
        )
      )
        return false;
      const maxW = getMaxKaTeXWidthLocal(opt.content);
      return maxW > contentAvailable;
    });

    const next = existsWideFormula
      ? "flex flex-col gap-y-4"
      : "grid grid-cols-2 gap-3";
    setLayoutClass(next);
    setLayoutType(existsWideFormula ? 0 : 1);

    // 如果回退到单列，计算公式缩放比例
    if (existsWideFormula) {
      setFormulaScales(optionListWithScales);
    } else {
      setFormulaScales(new Map());
    }

    setIsLayoutReady(true);
  }, [optionList]);

  // 初次与依赖变化时测量（使用 layoutEffect 保证先计算再显现，避免闪烁）
  useLayoutEffect(() => {
    setIsLayoutReady(false);
    const raf = requestAnimationFrame(measureAndDecideLayout);
    return () => cancelAnimationFrame(raf);
  }, [measureAndDecideLayout]);

  // 监听容器尺寸变化
  useEffect(() => {
    const el = containerRef.current;
    if (!el) return;
    let tid: number | null = null;
    const ro = new ResizeObserver(() => {
      if (tid) window.clearTimeout(tid);
      tid = window.setTimeout(() => measureAndDecideLayout(), 50);
    });
    ro.observe(el);
    return () => {
      ro.disconnect();
      if (tid) window.clearTimeout(tid);
    };
  }, [measureAndDecideLayout]);

  return (
    <>
      <div
        ref={containerRef}
        className={cn(
          "options-list font-[500]",
          // 为了避免布局切换时闪烁：先隐藏，测完再显
          isLayoutReady ? "opacity-100" : "opacity-0",
          "transition-opacity duration-200"
        )}
      >
        <div className={layoutClass}>
          {optionList.map((option) => {
            const state = getOptionState(
              option.id,
              selectedOptions.includes(option.id)
            );
            const isAnimatingThisOption =
              animatingOptions.has(option.id) && showResult;

            // 一次性获取所有样式，避免重复调用
            const optionStyles = getOptionStyles(state);
            // 获取公式缩放比例
            const formulaScale = formulaScales.get(option.id);

            return (
              <div
                key={option.id}
                className="option-item-wrapper relative overflow-visible"
              >
                {isAnimatingThisOption && !isPreview && (
                  <CorrectAnswerAnimation
                    optionId={option.id}
                    isAnimating={isAnimatingThisOption}
                    className="h-full"
                  />
                )}

                <button
                  onClick={() => handleOptionClick(option.id)}
                  disabled={showResult}
                  className={cn(
                    optionStyles.className,
                    "option-button duration-50 relative h-full overflow-visible transition-all",
                    showResult && hasImages ? "pb-9" : ""
                  )}
                  style={optionStyles.style}
                >
                  <div className="option-content-container flex h-full flex-col">
                    <div className="option-text-image-container relative flex-1 text-left">
                      <div
                        className={cn(
                          optionStyles.textColorClass,
                          "option-text-content flex w-full justify-between gap-3"
                        )}
                      >
                        {isJudgmentQuestion ? (
                          <JudgmentOptionText
                            optionKey={option.key}
                            isEnglish={isEnglish}
                            className="judgment-option-text"
                          />
                        ) : (
                          <>
                            <div className="flex flex-row items-start leading-[190%]">
                              {isMultipleChoiceQuestion && (
                                <div className="mr-3 mt-[1px] inline-flex h-[1.9em] items-center justify-center">
                                  <Checkbox
                                    isChecked={selectedOptions.includes(
                                      option.id
                                    )}
                                  />
                                </div>
                              )}
                              {option.key}.
                            </div>
                            <div
                              className="flex max-w-full flex-1 items-center"
                              style={{
                                // 新增：使用 font-size 的 em 形式进行缩放
                                fontSize:
                                  formulaScale &&
                                  formulaScale < 1 &&
                                  formulaScale > 0
                                    ? `${formulaScale}em`
                                    : undefined,
                              }}
                            >
                              <FormatMath
                                htmlContent={option.content}
                                questionId={questionId}
                                imageProcess={{
                                  enableLayout: true,
                                  enablePreview: false,
                                  imagesPerRow: 1,
                                }}
                                className={cn(
                                  "w-full max-w-full !leading-[175%] [&_.formatted-image-group]:!w-[100%] [&_.image-layout-item]:!h-[100%] [&_.image-layout-item]:!pb-[12px] [&_.katex-html]:!font-[500] [&_img]:my-2 [&_img]:w-full",
                                  optionStyles.textColorClass
                                )}
                              />
                            </div>
                          </>
                        )}

                        {showResult && layoutType != 1 && (
                          <StateIndicator
                            hasIncorrect={hasIncorrect}
                            state={state}
                            optionKey={option.key}
                            getOptionPercentage={getOptionPercentage}
                            questionRenderType={questionRenderType}
                            layoutType={layoutType}
                          />
                        )}
                      </div>
                    </div>
                  </div>

                  {showResult && layoutType == 1 && (
                    <StateIndicator
                      className="absolute bottom-0 left-0 w-full pb-2 pt-0"
                      hasIncorrect={hasIncorrect}
                      state={state}
                      optionKey={option.key}
                      getOptionPercentage={getOptionPercentage}
                      questionRenderType={questionRenderType}
                      layoutType={layoutType}
                    />
                  )}
                </button>
              </div>
            );
          })}
        </div>
      </div>

      {showResult && (
        <div className="correct-answer-display mt-6 flex w-full flex-col">
          <div className="correct-answer-text-container flex w-full flex-row items-start justify-center gap-2.5">
            <div className="text-text-1 correct-answer-label whitespace-nowrap text-[1.0625rem] font-bold leading-[1.25em]">
              正确答案:
            </div>
            <div className="correct-answer-value flex min-h-7 flex-1 text-[1.0625rem] font-bold leading-[1.25em] text-[var(--color-green-text)]">
              {isJudgmentQuestion ? (
                <JudgmentOptionText
                  optionKey={correctAnswerKeys[0] ?? ""}
                  isEnglish={isEnglish}
                />
              ) : (
                correctAnswerKeys.join("、")
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};
