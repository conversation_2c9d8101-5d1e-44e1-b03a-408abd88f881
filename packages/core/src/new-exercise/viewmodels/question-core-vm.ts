"use client";

import { ApiGetNextQuestionData } from "@repo/core/new-exercise/types/question";

import { useCallback, useEffect, useRef } from "react";
import { AnswerFeedbackType } from "../enums";
import { useGetNextQuestion } from "../models";
// 🔥 使用 Signal store 替换 useState
import { StudyType } from "@repo/core/enums";
import * as Sentry from "@sentry/nextjs";
import { QuestionStoreType } from "../store";
import { ExerciseInitParams } from "../types";
import {
  isChoiceQuestionType,
  isMultipleChoiceQuestionType,
  isObjectiveQuestionType,
  isSelfEvaluationQuestionType,
  isSubjectiveQuestionType,
} from "../utils/question-is";
import { useAnimationTransitionVM } from "./animation-transition-vm";

type QuestionViewModelProps = {
  exerciseParams: ExerciseInitParams;
  questionStore: QuestionStoreType;
  trackEventWithExercise: (event: string, params: Record<string, any>) => void;
};
const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export function useQuestionViewModel({
  exerciseParams,
  questionStore,
  trackEventWithExercise,
}: QuestionViewModelProps) {
  const { studySessionId, studyType, onComplete, isExerciseActive, isPreview } =
    exerciseParams;
  // 🔥 使用 Signal store 替换 useState
  const {
    progressBarState,
    lastSubmitResult,
    initialQuestionData,
    isLoading,
    hasNextQuestion,
    initialQuestionInfo,
    timerState,
    handleNextQuestion,
    rootQuestion,
    questionStatus,
  } = questionStore;
  const { executeTransitionSequence, nextTransition } =
    useAnimationTransitionVM({ questionStore, trackEventWithExercise });

  // 使用手动触发模式的useGetNextQuestion
  const { getNextQuestion } = useGetNextQuestion({
    firstQuestionData: initialQuestionData.value || undefined,
    studyType: studyType,

    studySessionId: studySessionId,
  });

  // 🔥 新增：跟踪 Resume 动画播放状态，防止重复播放
  const hasPlayedResumeAnimationRef = useRef(false);

  const handleResumeAnimation = useCallback(
    async (data: ApiGetNextQuestionData) => {
      const groupInfo = data?.nextQuestionGroupInfo;

      // 如果没有题组信息，或者题组数量大于 1
      const isShowGroupTransition =
        studyType == StudyType.REINFORCEMENT_EXERCISE &&
        groupInfo?.groupName &&
        groupInfo?.groupCount &&
        groupInfo?.groupCount > 0;

      if (!isExerciseActive) {
        return;
      }
      let isShowResumeTransition =
        !hasPlayedResumeAnimationRef.current && data?.isResume;

      if (studyType == StudyType.REINFORCEMENT_EXERCISE) {
        // 巩固练习情况下，如果显示题组转场，则不显示恢复练习转场
        isShowResumeTransition = !isShowGroupTransition;
      }

      if (isShowGroupTransition) {
        // TODO: 巩固练习场景下，需要等待客户端的动画播完，临时方案
        if (!data?.isResume) {
          await sleep(4200);
        }

        // 显示题组转场
        await executeTransitionSequence({
          groupInfo: groupInfo,
        });
      }

      // 如果未播放过动画，则播放恢复练习转场动画
      if (isShowResumeTransition) {
        hasPlayedResumeAnimationRef.current = true; // 标记已播放，防止重复
        executeTransitionSequence({
          specialFeedbacks: [
            {
              type: AnswerFeedbackType.Resume,
              title: "让我们继续练习",
            },
          ],
        });
      }
    },
    [isExerciseActive, executeTransitionSequence, studyType]
  );

  // 🔧 自动获取初始题目逻辑（只在没有firstQuestionData时执行一次）
  useEffect(() => {
    if (isPreview) {
      return;
    }
    if (studyType === StudyType.AI_COURSE) {
      return;
    }
    if (!initialQuestionData.value && !isLoading.value && !rootQuestion.value) {
      isLoading.value = true;
      // 🔥 直接调用API，避免函数依赖
      getNextQuestion()
        .then((data) => {
          Sentry.addBreadcrumb({
            category: "getNextQuestion",
            message: "获取下一题",
            data: {
              data,
            },
          });
          isLoading.value = false;

          const hasNextQuestion =
            data?.hasNextQuestion || data?.questionInfo?.questionId;

          if (!hasNextQuestion) {
            return onComplete?.();
          }

          initialQuestionInfo(data);
          handleResumeAnimation(data);

          progressBarState.value = {
            activeType: "default",
            progress: data?.progressInfo?.currentProgress || 0,
          };

          // 🆕 调用 onQuestionChange 回调，通知外部题目已获取
          if (exerciseParams.onQuestionChange && data?.questionInfo) {
            exerciseParams.onQuestionChange({
              questionData: data.questionInfo,
              index: 0, // 自动获取的第一题索引为 0
              questionStatus: questionStatus.value,
              questionType: {
                isObjective: isObjectiveQuestionType(data.questionInfo),
                isSubjective: isSubjectiveQuestionType(data.questionInfo),
                isChoice: isChoiceQuestionType(data.questionInfo),
                isMultipleChoice: isMultipleChoiceQuestionType(
                  data.questionInfo
                ),
                isSelfEvaluation: isSelfEvaluationQuestionType(
                  data.questionInfo
                ),
              },
            });
          }
        })
        .catch((_error) => {
          isLoading.value = false;
        });
    }
  }, [
    studyType,
    initialQuestionData.value,
    studySessionId,
    getNextQuestion,
    onComplete,
    exerciseParams,
    isPreview,
    handleResumeAnimation,
    initialQuestionInfo,
    progressBarState,
    initialQuestionData,
    isLoading,
    rootQuestion,
    questionStatus,
  ]); // 不包含任何函数依赖

  const handleContinue = useCallback(() => {
    // 🎯 累加当前题目时间到总时间（你的优化方案）
    const currentTime = timerState.value.currentTime;
    if (currentTime > 0) {
      timerState.value = {
        ...timerState.value,
        totalTime: timerState.value.totalTime + currentTime,
        currentTime: 0, // 重置当前题目时间
      };
      console.log(
        `[Timer] 题目完成，累加时间: ${currentTime}ms，总时间: ${timerState.value.totalTime}ms`
      );
    }

    // 🔥 简化逻辑：直接检查 nextQuestion state
    if (hasNextQuestion.value) {
      // 🔥 直接使用预处理好的数据，一步到位
      handleNextQuestion();
    } else {
      if (onComplete) {
        onComplete?.();
      }
    }
  }, [onComplete, timerState, handleNextQuestion, hasNextQuestion]);

  // 🔥 简化的转场处理逻辑 - 检查是否有下一题，没有则跳过动画直接完成
  const handleContinueWithTransitions = async () => {
    // 🔧 检查是否有下一题
    if (!hasNextQuestion.value) {
      onComplete?.();
      return;
    }

    // 获取转场数据
    const groupInfo = lastSubmitResult.value?.nextQuestionGroupInfo;
    const specialFeedbacks =
      lastSubmitResult.value?.specialFeedbacks?.filter(
        (item) =>
          item.type === AnswerFeedbackType.DifficultyDown ||
          item.type === AnswerFeedbackType.DifficultyUp
      ) || [];
    // 执行转场序列（只有在有下一题时才播放动画）
    await executeTransitionSequence({
      groupInfo: groupInfo || undefined,
      specialFeedbacks: specialFeedbacks,
    });

    // 转场播放完成或无需转场，切换题目
    handleContinue();
  };

  return {
    // ✅ 状态更新方法（ViewModel 专注状态管理）
    handleContinue,
    handleContinueWithTransitions,
    nextTransition,
  };
}
