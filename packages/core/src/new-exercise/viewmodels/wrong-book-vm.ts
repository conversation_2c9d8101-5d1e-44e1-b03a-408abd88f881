/**
 * 错题本 ViewModel - Signal 版本
 * 基于 Preact Signals 的高性能错题本状态管理
 */

import { useComputed, useSignal } from "@preact-signals/safe-react";
import { StudyType } from "@repo/core/enums";
import { useCallback, useEffect, useState } from "react";
import { toast } from "../../../../../apps/stu/app/components/common/toast";
import {
  convertTagNamesToIds,
  useAddWrongQuestion,
  useGetErrorReasonTags,
  useRemoveWrongQuestion,
} from "../models/wrong-book-model";

// 类型定义
export interface QuestionRecord {
  questionId: string;
  isAnswerCorrect?: boolean; // true: 答对, false: 答错, undefined: 未答
  isInWrongBook: boolean; // 是否在错题本中
  isRemovedFromWrongBook: boolean; // 是否被用户手动移出错题本
  wrongQuestionId?: number; // 错题记录ID
  autoAddedToWrongBook?: boolean; // 是否是自动加入错题本的（用于控制 tooltip 显示）
  errorReasonTags?: string[]; // 错因标签
  notes?: string; // 备注
  toastShown?: boolean; // 🆕 是否已显示过 toast 提示
}

export interface WrongQuestionBookState {
  wrongQuestionBook: any; // Signal<Set<string>>
  wrongQuestionIdMap: any; // Signal<Map<string, number>>
  questionRecords: any; // Signal<Map<string, QuestionRecord>>
  isLoading: any; // Signal<boolean>
  error: any; // Signal<Error | null>
}

export interface WrongQuestionBookActions {
  handleToggleWrongQuestionBook: (
    questionId: string,
    isCurrentlyInBook: boolean,
    options?: {
      errorReasonTags?: string[];
      errorReasonTagIds?: number[];
      reasonTitle?: string;
      reasonSubTitle?: string;
      notes?: string;
    }
  ) => Promise<void>;
  isInWrongQuestionBook: (questionId: string) => boolean;
  recordQuestionAnswer: (
    questionId: string,
    isAnswerCorrect?: boolean,
    wrongTimes?: number,
    isFinalAnswer?: boolean,
    isGiveUp?: boolean,
    verifyType?: number,
    answerVerify?: number
  ) => void;
  getQuestionRecord: (questionId: string) => QuestionRecord | undefined;
  shouldAutoShowTooltip: (questionId: string) => boolean;
  markTooltipAsShown: (questionId: string) => void;
  getQuestionErrorReasonTags: (questionId: string) => string[];
  getQuestionNotes: (questionId: string) => string;
}

export interface UseWrongQuestionBookOptions {
  /** 初始错题本数据 */
  initialWrongQuestions?: Array<{
    questionId: string;
    wrongQuestionId: number;
  }>;
  /** 学习类型 */
  studyType?: StudyType;
  /** 是否为预览模式 */
  isPreview?: boolean;
}

/**
 * 错题本 ViewModel Hook - Signal 版本
 */
export function useWrongQuestionBookVm(
  options: UseWrongQuestionBookOptions = {}
) {
  const { initialWrongQuestions = [], studyType, isPreview = false } = options;

  // 使用新的 hook
  const { addWrongQuestion, isAdding } = useAddWrongQuestion();
  const { removeWrongQuestion, isRemoving } = useRemoveWrongQuestion();
  // 使用统一的错因标签 Hook，默认不自动请求
  const { data: errorReasonTags, getErrorReasonTags } = useGetErrorReasonTags();

  // Signal 状态管理
  const wrongQuestionBookSignal = useSignal<Set<string>>(new Set());
  const wrongQuestionIdMapSignal = useSignal<Map<string, number>>(new Map());
  const questionRecordsSignal = useSignal<Map<string, QuestionRecord>>(
    new Map()
  );
  const errorSignal = useSignal<Error | null>(null);

  // 强制重新渲染的状态
  const [, setForceUpdate] = useState(0);

  // 🆕 使用 useState 来追踪状态变化，强制重新计算
  const [, setStateVersion] = useState(0);

  // 🆕 在状态更新时增加版本号
  const incrementStateVersion = useCallback(() => {
    setStateVersion((prev) => prev + 1);
  }, []);

  // 计算属性 - 合并加载状态
  const isLoading = useComputed(() => isAdding || isRemoving);

  // 初始化错题本数据
  useEffect(() => {
    if (initialWrongQuestions.length > 0) {
      // 原有的初始化逻辑
      const questionIds = new Set(
        initialWrongQuestions.map((item) => item.questionId.toString())
      );
      const idMap = new Map(
        initialWrongQuestions.map((item) => [
          item.questionId.toString(),
          item.wrongQuestionId,
        ])
      );

      // 初始化题目记录
      const records = new Map<string, QuestionRecord>();
      initialWrongQuestions.forEach((item) => {
        const questionId = item.questionId.toString();
        records.set(questionId, {
          questionId,
          isAnswerCorrect: false, // 初始数据假设为答错
          isInWrongBook: true,
          isRemovedFromWrongBook: false,
          wrongQuestionId: item.wrongQuestionId,
          toastShown: true, // 🆕 初始错题本数据不需要显示 toast
        });
      });

      wrongQuestionBookSignal.value = questionIds;
      wrongQuestionIdMapSignal.value = idMap;
      questionRecordsSignal.value = records;
    }
  }, [
    initialWrongQuestions,
    wrongQuestionBookSignal,
    wrongQuestionIdMapSignal,
    questionRecordsSignal,
  ]);

  // 🆕 判断是否是相似题的工具函数
  const isSimilarQuestion = useCallback(
    (
      questionId: string,
      wrongQuestionInfo?: { recommendQuestionIds?: string[] }
    ) => {
      return (
        wrongQuestionInfo?.recommendQuestionIds?.includes(questionId) ?? false
      );
    },
    []
  );

  // 🆕 创建一个可以被 React 组件订阅的 Signal，用于自动显示 tooltip
  const autoShowTooltipSignal = useSignal<string | null>(null);

  // 记录题目答题结果
  const recordQuestionAnswer = useCallback(
    (
      questionId: string,
      isAnswerCorrect?: boolean,
      wrongTimes?: number, // 🆕 新增 wrongTimes 参数
      isFinalAnswer?: boolean, // 🆕 新增 isFinalAnswer 参数
      isGiveUp?: boolean, // 🔑 新增 isGiveUp 参数，用于标识放弃作答
      verifyType?: number, // 🔑 新增 verifyType 参数，用于区分题目类型
      answerVerify?: number // 🔑 新增 answerVerify 参数，用于判断主观题的判题状态
    ) => {
      const records = new Map(questionRecordsSignal.value);
      const existingRecord = records.get(questionId);

      if (existingRecord) {
        // 更新现有记录
        existingRecord.isAnswerCorrect = isAnswerCorrect;
      } else {
        // 创建新记录
        const newRecord: QuestionRecord = {
          questionId,
          isAnswerCorrect,
          // 🆕 如果是错题本练习或错题本查看模式，默认题目就在错题本中
          isInWrongBook: studyType === StudyType.WRONG_QUESTION_BANK,
          isRemovedFromWrongBook: false,
          toastShown: false, // 🆕 新题目默认未显示过 toast
        };
        records.set(questionId, newRecord);
      }

      // 🆕 根据 wrongTimes 控制错题本行为
      // 🔑 对于主观题的判断逻辑
      const isSubjectiveQuestion = verifyType === 2;
      const shouldAddToWrongBookForSubjective = isSubjectiveQuestion
        ? // 主观题：answerVerify !== 99（已自评）且 answerVerify !== 1（自评不是正确）
          answerVerify !== 99 && answerVerify !== 1
        : isAnswerCorrect === false || isAnswerCorrect === undefined; // 客观题：答案错误时加入错题本

      const shouldAddToWrongBook =
        shouldAddToWrongBookForSubjective &&
        studyType !== StudyType.WRONG_QUESTION_BANK;

      if (shouldAddToWrongBook) {
        const record = records.get(questionId)!;

        // 🆕 简化逻辑：只要答错了就应该加入错题本，不管是第几次答错
        // 唯一的例外是：如果用户手动移出过，需要重新加入
        record.isInWrongBook = true;

        // 🔑 保留首次答错的判断，用于toast显示逻辑
        const isFirstTimeWrong = wrongTimes === 0 || wrongTimes === undefined;

        // 🔑 简化逻辑：只要答错了就应该加入错题本
        // 如果之前被手动移出过，需要重新设置状态
        if (record.isRemovedFromWrongBook) {
          record.isRemovedFromWrongBook = false; // 重置移出状态
        }

        // 🆕 判断是否应该显示 toast - 保持原有逻辑
        // 条件：首次答错 且 答案错误 且 该题目从未显示过 toast
        // 🔑 放弃作答时也显示 toast
        // 🔑 主观题特殊处理：answerVerify !== 99（已自评）且 answerVerify !== 1（自评不是正确）
        const shouldShowToastForAnswer = isSubjectiveQuestion
          ? // 主观题：answerVerify !== 99（已自评）且 answerVerify !== 1（自评不是正确）
            answerVerify !== 99 && answerVerify !== 1
          : isAnswerCorrect === false || isGiveUp === true; // 客观题：答案错误或放弃时显示 toast

        const shouldShowToast =
          isFirstTimeWrong &&
          shouldShowToastForAnswer &&
          record.toastShown !== true;

        // 🔑 详细调试信息
        console.log("🔍 Toast 显示条件检查:", {
          questionId,
          isFirstTimeWrong,
          isAnswerCorrect,
          isGiveUp,
          toastShown: record.toastShown,
          verifyType,
          answerVerify,
          isSubjectiveQuestion,
          shouldShowToastForAnswer,
          shouldShowToast,
          wrongTimes,
          studyType,
          shouldAddToWrongBook,
          isRemovedFromWrongBook: record.isRemovedFromWrongBook,
          // 🔑 主观题特殊检查
          hasEvaluated: answerVerify !== 99,
          answerVerifyValue: answerVerify,
          shouldShowForSubjective: isSubjectiveQuestion
            ? answerVerify !== 99 && answerVerify !== 1
            : "N/A",
        });

        // 🆕 判断是否应该显示 tooltip
        // 条件：答案错误（无论是否有二次作答机会）
        // 🔑 放弃作答时也显示 tooltip
        // 🔑 主观题特殊处理：answerVerify !== 99（已自评）且 answerVerify !== 1（自评不是正确）
        const shouldShowTooltipForAnswer = isSubjectiveQuestion
          ? // 主观题：answerVerify !== 99（已自评）且 answerVerify !== 1（自评不是正确）
            answerVerify !== 99 && answerVerify !== 1
          : isAnswerCorrect === false || isGiveUp === true; // 客观题：答案错误或放弃时显示 tooltip

        const shouldShowTooltip = shouldShowTooltipForAnswer;

        record.autoAddedToWrongBook = shouldShowTooltip;

        // 🆕 更新错题本状态 - 只要答错了就加入
        const newWrongQuestionBook = new Set(wrongQuestionBookSignal.value);
        newWrongQuestionBook.add(questionId);
        wrongQuestionBookSignal.value = newWrongQuestionBook;

        // 🔑 答错时显示 toast 提示（每道题只显示一次，但二次答错重新加入时可以再次显示）
        if (shouldShowToast) {
          // toast.show("已加入错题本");
          // 🔑 标记该题目已显示过 toast
          record.toastShown = true;
        }

        // 🔑 答错时显示 tooltip（无论是否有二次作答机会）
        if (shouldShowTooltip) {
          // 🔑 延迟触发 tooltip 显示，确保按钮状态先更新
          setTimeout(() => {
            // 先清空 Signal，然后设置新值，确保触发变化
            autoShowTooltipSignal.value = null;

            setTimeout(() => {
              autoShowTooltipSignal.value = questionId;
            }, 10);
          }, 300);
        }
      }

      questionRecordsSignal.value = records;
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [questionRecordsSignal, wrongQuestionBookSignal, studyType]
  );

  // 获取题目记录（保留，可能在调试时有用）
  const getQuestionRecord = useCallback(
    (questionId: string): QuestionRecord | undefined => {
      return questionRecordsSignal.value.get(questionId);
    },
    [questionRecordsSignal]
  );

  // 🆕 判断是否应该自动显示 tooltip
  const shouldAutoShowTooltip = useCallback(
    (questionId: string): boolean => {
      const record = questionRecordsSignal.value.get(questionId);
      const result = record?.autoAddedToWrongBook === true;

      // 如果应该自动显示，更新 Signal 来触发 React 组件重新渲染
      if (result && autoShowTooltipSignal.value !== questionId) {
        autoShowTooltipSignal.value = questionId;
      }

      return result;
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [questionRecordsSignal]
  );

  // 🆕 标记 tooltip 已显示
  const markTooltipAsShown = useCallback(
    (questionId: string) => {
      const records = new Map(questionRecordsSignal.value);
      const record = records.get(questionId);
      if (record) {
        record.autoAddedToWrongBook = false;
        questionRecordsSignal.value = records;
      }
    },
    [questionRecordsSignal]
  );

  // 🆕 获取题目的错因标签
  const getQuestionErrorReasonTags = useCallback(
    (questionId: string): string[] => {
      const record = questionRecordsSignal.value.get(questionId);

      // 🆕 如果题目已被移出错题本，返回空数组
      if (!record || record.isRemovedFromWrongBook) {
        return [];
      }

      const tags = record.errorReasonTags || [];

      return tags;
    },
    [questionRecordsSignal]
  );

  // 🆕 获取题目的备注
  const getQuestionNotes = useCallback(
    (questionId: string): string => {
      const record = questionRecordsSignal.value.get(questionId);

      // 🆕 如果题目已被移出错题本，返回空字符串
      if (!record || record.isRemovedFromWrongBook) {
        return "";
      }

      return record.notes ?? "";
    },
    [questionRecordsSignal]
  );

  // 错题本切换处理函数
  const handleToggleWrongQuestionBook = useCallback(
    async (
      questionId: string,
      isCurrentlyInBook: boolean,
      options?: {
        errorReasonTags?: string[];
        errorReasonTagIds?: number[];
        reasonTitle?: string;
        reasonSubTitle?: string;
        notes?: string;
      }
    ) => {
      if (!questionId || questionId.trim() === "") {
        return;
      }

      errorSignal.value = null;

      try {
        if (isCurrentlyInBook) {
          // 从错题本移除 - 只是打标记，不真正移除
          const records = new Map(questionRecordsSignal.value);
          const record = records.get(questionId);

          if (record) {
            record.isRemovedFromWrongBook = true;
            // 🆕 移出错题本时清空错因标签和备注
            record.errorReasonTags = [];
            record.notes = "";
          } else {
            // 🆕 如果记录不存在，创建一个新的记录来标记移出状态
            records.set(questionId, {
              questionId,
              isInWrongBook: false,
              isRemovedFromWrongBook: true,
              errorReasonTags: [], // 确保错因标签为空
              notes: "", // 确保备注为空
              toastShown: true, // 🆕 移出状态的记录不需要显示 toast
            });
          }

          questionRecordsSignal.value = records;

          // 更新显示状态（从界面上移除）
          const newSet = new Set(wrongQuestionBookSignal.value);
          newSet.delete(questionId);
          wrongQuestionBookSignal.value = newSet;

          const newMap = new Map(wrongQuestionIdMapSignal.value);
          newMap.delete(questionId);
          wrongQuestionIdMapSignal.value = newMap;

          // 调用API移除（如果需要）
          await removeWrongQuestion({
            questionId,
          });
          // 🔧 用户主动移出错题本时显示提示
          toast.show("已移出错题本");

          // 强制重新渲染
          setForceUpdate((prev) => prev + 1);
        } else {
          // 添加到错题本
          // 如果传入了错因标签名称，需要转换为ID
          let reasonTagList: number[] = options?.errorReasonTagIds ?? [];
          if (options?.errorReasonTags && options.errorReasonTags.length > 0) {
            // 如果没有错因标签数据，先请求
            let tagsData = errorReasonTags;
            if (!tagsData) {
              tagsData = await getErrorReasonTags();
            }

            if (tagsData !== null && tagsData !== undefined) {
              reasonTagList = convertTagNamesToIds(
                options.errorReasonTags,
                tagsData
              );
            }
          }

          await addWrongQuestion({
            questionId,
            reasonTitle: (options?.reasonTitle ?? options?.notes ?? "").trim(),
            reasonSubTitle: (options?.reasonSubTitle ?? "").trim(),
            reasonTagList, // 使用转换后的标签ID
          });

          // 🔧 用户主动加入错题本时显示提示
          // toast.show("已加入错题本");

          // 更新本地状态
          const newSet = new Set(wrongQuestionBookSignal.value);
          newSet.add(questionId);
          wrongQuestionBookSignal.value = newSet;

          // 同时更新题目记录，保存错因标签和备注信息
          const records = new Map(questionRecordsSignal.value);
          const existingRecord = records.get(questionId);

          if (existingRecord) {
            existingRecord.isInWrongBook = true;
            existingRecord.isRemovedFromWrongBook = false;
            existingRecord.errorReasonTags = options?.errorReasonTags ?? [];
            existingRecord.notes = options?.notes ?? "";
          } else {
            const newRecord = {
              questionId,
              isInWrongBook: true,
              isRemovedFromWrongBook: false,
              errorReasonTags: options?.errorReasonTags ?? [],
              notes: options?.notes ?? "",
              toastShown: true, // 🆕 手动添加的题目不需要显示 toast
            };
            records.set(questionId, newRecord);
          }
          questionRecordsSignal.value = records;

          // 强制重新渲染
          setForceUpdate((prev) => prev + 1);
          // 🔑 强制触发状态更新
          incrementStateVersion();
        }
      } catch (error) {
        const err = error as Error;

        errorSignal.value = err;

        // 注意：toast 已经在 hook 中处理了，这里不需要重复显示
        throw err; // 重新抛出错误，让调用方也能处理
      }
    },
    [
      addWrongQuestion,
      removeWrongQuestion,
      errorSignal,
      wrongQuestionBookSignal,
      wrongQuestionIdMapSignal,
      questionRecordsSignal,
      errorReasonTags,
      getErrorReasonTags,
      setForceUpdate, // 🔑 添加 setForceUpdate 依赖
      incrementStateVersion, // 🔑 添加 incrementStateVersion 依赖
    ]
  );

  // 判断题目是否在错题本中
  const isInWrongQuestionBook = useCallback(
    (questionId: string): boolean => {
      const record = questionRecordsSignal.value.get(questionId);

      // 🆕 检查是否为错题本相关场景
      const isErrBookPreview =
        typeof window !== "undefined" &&
        window.location.pathname.includes("err-book-exercise-preview-entry");

      const isWrongBookContext =
        isErrBookPreview || studyType === StudyType.WRONG_QUESTION_BANK;

      // 错题本相关场景：默认在错题本中，除非用户手动移出
      if (isWrongBookContext) {
        return !record || !(record.isRemovedFromWrongBook === true);
      }

      // 其他练习类型：需要明确在错题本中且未被移出
      if (record) {
        return record.isInWrongBook && !record.isRemovedFromWrongBook;
      }

      // 兜底：检查 Signal 中是否存在
      return wrongQuestionBookSignal.value.has(questionId);
    },
    [wrongQuestionBookSignal, questionRecordsSignal, studyType]
  );

  // 返回状态和操作函数
  return {
    // 状态 - 返回 Signal 本身以保持响应式
    wrongQuestionBook: wrongQuestionBookSignal,
    wrongQuestionIdMap: wrongQuestionIdMapSignal,
    questionRecords: questionRecordsSignal,
    isLoading: isLoading,
    error: errorSignal,

    // 操作函数
    handleToggleWrongQuestionBook,
    isInWrongQuestionBook,
    recordQuestionAnswer,
    getQuestionRecord,
    isSimilarQuestion, // 🆕 暴露相似题判断函数
    shouldAutoShowTooltip, // 🆕 判断是否应该自动显示 tooltip
    markTooltipAsShown, // 🆕 标记 tooltip 已显示
    getQuestionErrorReasonTags, // 🆕 获取题目的错因标签
    getQuestionNotes, // 🆕 获取题目的备注
    autoShowTooltipSignal, // 🆕 用于触发 React 组件重新渲染的 Signal
  };
}
