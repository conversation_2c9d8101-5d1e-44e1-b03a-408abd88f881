import { ClassStatus, LearningStatus, SUBJECT } from "@/enums";

export interface GetCourseTableParams {
  /** 教师ID */
  teacher_id: number;
  /** 学校ID */
  school_id: number;
  /** 学年ID */
  school_year_id: number;
  /** 开始日期 YYYY-MM-DD */
  start_date: string;
  /** 结束日期 YYYY-MM-DD */
  end_date: string;
}

export interface Schedule {
  /** 课程表ID */
  schedule_id: number;
  /** 年级 */
  grade: number;
  /** 年级名称 */
  grade_name: string;
  /** 班级ID */
  class_id: number;
  /** 班级名称 */
  class_name: string;
  /** 时间段（上午/下午/晚上） */
  schedule_tpl_period_time_span: string;
  /** 开始时间 HH:mm:ss */
  schedule_tpl_period_start_time: string;
  /** 结束时间 HH:mm:ss */
  schedule_tpl_period_end_time: string;
  /** 课程ID */
  class_schedule_course_id: SUBJECT;
  /** 课程名称 */
  class_schedule_course: string;
  /** 教师ID */
  class_schedule_teacher_id: number;
  /** 教师姓名 */
  teacher_name: string;
  /** 学习类型 1:课程 2:学科自习 */
  class_schedule_study_type: number;
  /** 学习类型名称 */
  class_schedule_study_type_name: string;
  /** 是否临时课程 0:否 1:是 */
  is_tmp: number;
  /** 日期 */
  at_day: string;
  /** 原课程表ID */
  origin_schedule_id: number;
  /** 在时间范围内，可点击 */
  is_in_time_range: boolean;
  /** 是否在上课 */
  is_in_class: boolean;
  /** 课堂ID */
  classroom_id: string;
  /** 课程图标 */
  course_icon_url: string;
  /** 课程背景颜色 */
  course_bg_color: string;
  /** 课程边框颜色 */
  course_border_color: string;
  /** 课程图标颜色 */
  course_icon_color: string;
  /** 课程文本颜色 */
  course_text_color: string;
  /** 课程简称 */
  course_abbreviation: string;
}

export interface GetCourseTableResponse {
  /** 课程表数据，key为日期（YYYY-MM-DD），value为当天的课程列表 */
  schedule: Record<string, Schedule[]>;
}

export interface StudentInfo {
  /** 学生ID */
  studentId: number;
  /** 学生姓名 */
  studentName: string;
  /** 学生头像 */
  avatarUrl: string;
}

/** 课堂学习能量排行数据 */
export interface CourseRankingItem {
  /** 学生ID */
  student_id: number;
  /** 学生姓名 */
  student_name: string;
  /** 学生头像 */
  student_avatar: string;
  /** 学习能量 */
  score: number;
  /** 排名 */
  rank: number;
}

/** 学生行为类型 */
export interface BehaviorType {
  /** 行为类型ID */
  type: number;
  /** 行为类型名称 */
  name: string;
  /** 行为类型描述 */
  description: string;
  /** 行为类型图标 */
  icon: string;
}

/** 学生最新行为数据 */
export interface StudentLatestBehavior {
  /** 学生ID */
  student_id: number;
  /** 学生姓名 */
  student_name: string;
  /** 学生头像 */
  student_avatar: string;
  /** 行为类型 */
  behavior_type: number;
  /** 行为时间 */
  behavior_time: string;
  /** 行为描述 */
  behavior_description: string;
}

/** 学生课堂详情数据 */
export interface StudentCourseDetail {
  /** 基本信息 */
  basic: {
    student_id: number;
    student_name: string;
    student_avatar: string;
    class_name: string;
    seat_number: string;
  };
  /** 学习数据 */
  study: {
    score: number;
    rank: number;
    attention_rate: number;
    task_completion_rate: number;
  };
  /** 行为记录 */
  behaviors: {
    type: number;
    time: string;
    description: string;
  }[];
}

// 课堂学习能量排行的返回值类型
export interface CourseRankingData {
  classroomId: string;
  queryTime: number;
  students: {
    studentId: number;
    studentName: string;
    avatarUrl: string;
    learningScore: number;
    learningTime: number;
    correctCount: number;
    totalCount: number;
  }[];
}

export interface StudentBehaviorTag {
  type: string;
  count: number;
  text: string;
  isTrigger: boolean;
  detail: {
    timeRange: string;
    behaviorDesc: string;
  }[];
}

/** 关注学生 */
export interface AttentionStudent {
  studentId: number; // 学生ID
  studentName: string; // 学生姓名
  behaviorType: string; // 行为类型
  behaviorDesc: string; // 行为描述
  behaviorTags: StudentBehaviorTag[];
  reminderCount: number; // 已提醒次数
  totalQuestions: number; // 总题目数
  correctAnswers: number; // 正确题数
  wrongAnswers: number; // 错误题数
  accuracyRate: number; // 正确率(%)
  learningProgress: number; // 学习进度(%)
  lastUpdateTime: number; // 最后更新时间(UTC秒数)
  avatarUrl: string; // 头像URL
  isHandled: boolean; // 是否已处理
  handleTime: number; // 处理时间(UTC秒数)
}

/** 表扬学生 */
export interface PraiseStudent {
  studentId: number; // 学生ID
  studentName: string; // 学生姓名
  behaviorType: string; // 行为类型
  behaviorDesc: string; // 行为描述
  behaviorTags: StudentBehaviorTag[];
  praiseCount: number; // 表扬次数
  reminderCount: number; // 已提醒次数
  totalQuestions: number; // 总题目数
  correctAnswers: number; // 正确题数
  wrongAnswers: number; // 错误题数
  accuracyRate: number; // 正确率(%)
  learningProgress: number; // 学习进度(%)
  lastUpdateTime: number; // 最后更新时间(UTC秒数)
  avatarUrl: string; // 头像URL
  isHandled: boolean; // 是否已处理
  handleTime: number; // 处理时间(UTC秒数)
}

export interface LearningScore {
  avatarUrl: string;
  correctCount: number;
  handleTime: number;
  isHandled: boolean;
  learningScore: number;
  learningTime: number;
  studentId: number;
  studentName: string;
  totalCount: number;
}

export type StudentLearningStatusData = {
  accuracyRate: number;
  avatarUrl: string;
  curStudyContent: string;
  isOnline: boolean;
  lastUpdateTime: number;
  learningProgress: number;
  learningStatus: LearningStatus;
  // 是不是当前学科
  isLearningSubject: boolean;
  studentId: number;
  studentName: string;
};

// 课堂表扬关注提醒行为分类列表
export interface GetBehaviorCategorysResponse {
  classStatus: ClassStatus;
  query_time: string;
  classroomId: string;
  attentionList: AttentionStudent[];
  praiseList: PraiseStudent[];
  learningScores: LearningScore[];
  allStudentsList: StudentLearningStatusData[];
}

// 提醒学生的返回值类型
export interface RemindStudentData {
  classroomId: string;
  success: boolean;
  message: string;
  handleTime: number;
  results: {
    studentId: number;
    studentName: string;
    success: boolean;
    message: string;
  }[];
}

// 学生课堂详情的返回值类型
export interface StudentCourseDetailData {
  studentId: number;
  classroomId: string;
  schoolId: number;
  classId: number;
  totalStudyTime: number;
  classroomScore: number;
  maxCorrectStreak: number;
  questionCount: number;
  accuracyRate: number;
  interactionCount: number;
  violationCount: number;
  violationTime: number;
  /** 是否已评价 */
  isEvaluated: boolean;
  /** 评价内容 */
  evaluateContent: string;
  /** 评价推送时间 */
  pushTime: number;
  /** 是否已鼓励 */
  isHandled: boolean;
  learningRecords: {
    accuracyRate: number | number;
    chapterId: string;
    chapterName: string;
    createTime: number;
    duration: number;
    learningType: string;
    progress: number;
    recordId: string;
    taskId: number;
    assignId: number;
    startTime: number;
    taskName: string;
    deadline: number;
    taskType: number;
    typeName: string;
    subject: number;
    resourceId: number;
  }[];
}

// 检测是否上课的返回值类型
export interface IsTeachingData {
  is_teaching: boolean;
  schedule: Schedule | null;
}

// 课堂信息
export interface GetCourseInfoResponse {
  classroomId: string;
  gradeName: string;
  className: string;
  subject: string;
  subjects: {
    subjectKey: number;
    subjectName: string;
  }[];
  isInClass: boolean;
}

// 班级学生最新行为数据
export type GetStudentLearningStatusResponse = StudentLearningStatusData[];
