"use client";

import { Label } from "@/ui/label";
import { Switch } from "@/ui/switch";
import { AnswerFilter } from "../../store/answers";
// import { InputSearch } from "@/ui/searchInput";
import { useTaskContext } from "@/app/homework/[id]/_context/task-context";
import { questionTypeEnumManager } from "@/enums";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/ui/select";
import { umeng, UmengCategory, UmengHomeworkAction } from "@/utils";
import { useComputed } from "@preact-signals/safe-react";
import { useState } from "react";
import { useAnswerResults } from "../../store/answers";
export default function Toolbar() {
  const { taskType, viewMode, viewModeNew } = useTaskContext();
  const { filter, setFilter, answers, data, answerResultsState } =
    useAnswerResults();
  const [isOpen, setIsOpen] = useState(false);
  // 获取题目统计信息
  const statsInfo = useComputed(() => {
    // 获取总题目数
    const totalQuestions = answerResultsState.value.data?.totalCount || 0; //answers.value?.length || 0;

    // 获取错题数量
    let wrongQuestions = 0;

    if (viewMode.value === "student" || viewModeNew.value === "student") {
      // 学生视图：计算学生的错题数
      wrongQuestions = answers.value?.filter((q) => {
        return q.answerDetails.length > 0 && !q.answerDetails[0].isCorrect;
      }).length;
    } else {
      // 班级视图：直接使用 commonIncorrectCount 字段
      wrongQuestions = data.value?.commonIncorrectCount || 0;
    }

    return {
      totalQuestions,
      wrongQuestions,
    };
  });

  return (
    <div className="flex items-center justify-between gap-4 px-4 py-4 pr-6">
      <div className="flex flex-1 items-center">
        {/* <InputSearch
          debounce
          wait={300}
          classNames={{
            input: " rounded-[1.125rem] text-sm",
          }}
          className="w-50 h-8"
          placeholder="请输入题目关键词"
          value={filter.value.keyword}
          onChange={(value) => {
            setFilter({ keyword: value.target.value })
          }}

        /> */}
        <div className="text-gray-4 ml-4 flex items-center gap-2 text-[0.875rem] font-normal leading-[150%]">
          共{statsInfo.value.totalQuestions}道题，
          {statsInfo.value.wrongQuestions}道
          {viewMode.value === "student" || viewModeNew.value === "student"
            ? "错题"
            : "共性错题"}{" "}
          {(viewMode.value === "student" || viewModeNew.value === "student") &&
            "（按照已作答排序）"}
        </div>
      </div>

      <div className="flex items-center gap-5">
        <div className="flex items-center space-x-2">
          <Label
            htmlFor="common-mistakes"
            className="text-gray-2 text-xs font-normal"
          >
            {viewMode.value === "student" || viewModeNew.value === "student"
              ? "只看错题"
              : "只看共性错题"}
          </Label>
          <Switch
            id="common-mistakes"
            checked={
              viewMode.value === "student" || viewModeNew.value === "student"
                ? isOpen
                : !filter.value.allQuestions
            }
            className="cursor-pointer"
            onCheckedChange={(checked) => {
              // DONE: 埋点23 => `homework_list_report_common_mistakes_click` 用户查看共性错题
              umeng.trackEvent(
                UmengCategory.HOMEWORK,
                UmengHomeworkAction.REPORT_COMMON_MISTAKES_CLICK,
                {
                  isOpen: checked,
                }
              );
              setIsOpen(checked);
              setFilter({ allQuestions: !checked });
            }}
          />
        </div>

        {viewMode.value !== "student" && viewModeNew.value !== "student" && (
          <Select
            value={filter.value.sortBy}
            onValueChange={(value) =>
              setFilter({ sortBy: value as AnswerFilter["sortBy"] })
            }
          >
            <SelectTrigger
              className="cursor-pointer border-none px-0 text-xs shadow-none"
              classNames={{ icon: "text-gray-2" }}
            >
              <SelectValue placeholder="排序方式" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="answerCount" className="cursor-pointer">
                按作答人数排序
              </SelectItem>

              {taskType.value === 10 && (
                <>
                  <SelectItem value="incorrectCount" className="cursor-pointer">
                    按答错人数排序
                  </SelectItem>
                </>
              )}
              {taskType.value === 20 && (
                <>
                  <SelectItem value="orderByAssign" className="cursor-pointer">
                    按布置顺序排序
                  </SelectItem>
                </>
              )}
            </SelectContent>
          </Select>
        )}

        <Select
          value={filter.value.questionType?.toString() || "0"}
          onValueChange={(value) =>
            setFilter({ questionType: parseInt(value) })
          }
        >
          <SelectTrigger
            className="cursor-pointer border-none px-0 text-xs shadow-none"
            classNames={{ icon: "text-gray-2" }}
          >
            <SelectValue placeholder="题目类型" />
          </SelectTrigger>
          <SelectContent>
            {questionTypeEnumManager.enums.map((item) => (
              <SelectItem key={item.value} value={item.value.toString()}>
                {item.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
