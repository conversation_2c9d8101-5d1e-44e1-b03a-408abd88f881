"use client";

import CloseIcon from "@/public/icons/ic_close.svg";
import { StudentBehaviorTag, StudentInfo } from "@/types/course";
import { ScrollArea } from "@/ui/scroll-area";
import Avatar from "@/ui/tch-avatar";
import { SheetClose } from "@/ui/tch-sheet";

export function TagDetail(props: {
  tag: StudentBehaviorTag;
  setTag: (tag?: StudentBehaviorTag) => void;
  student: StudentInfo & { classId?: number };
  setStudent: (student: StudentInfo | null) => void;
}) {
  return (
    <div className="flex h-full flex-col leading-normal">
      <div className="bg-fill-gray-2 h-18 text-gray-1 flex items-center justify-between px-6">
        <span className="flex items-center gap-2 text-base/normal font-semibold">
          <Avatar
            src={props.student.avatarUrl}
            alt="avatar"
            className="size-9"
          />
          {props.student.studentName}
        </span>

        <SheetClose>
          <CloseIcon
            onClick={() => {
              props.setStudent(null);
              props.setTag();
            }}
            className="h-5 w-5 cursor-pointer active:opacity-80"
          />
        </SheetClose>
      </div>

      <ScrollArea
        className="bg-fill-gray-2 flex-1 overflow-hidden"
        viewportClassName="[&>div:first-child]:flex! [&>div:first-child]:flex-col [&>div:first-child]:min-h-full"
      >
        <div className="px-6 pb-6">
          <div className="border-line-1 bg-fill-white overflow-hidden rounded-2xl border">
            {/* 标题区域 */}
            <div className="p-5 pb-6">
              <div className="bg-orange-5 inline-flex h-9 items-center rounded-[1.125rem] pl-5 pr-5">
                <span className="text-orange-1 text-base font-medium leading-6">
                  {props.tag.text}
                </span>
              </div>
            </div>

            {/* 表格区域 - 使用 CSS Grid */}
            <div className="border-line-3 mb-5 ml-5 mr-5 overflow-hidden rounded-xl border">
              {/* 表头 */}
              <div className="bg-fill-gray-2 grid grid-cols-[9.25rem_1fr]">
                <div className="border-line-3 flex items-center justify-center border-b border-r py-2">
                  <span className="text-gray-2 text-base font-normal leading-7">
                    时间
                  </span>
                </div>
                <div className="flex items-center justify-center border-b py-2">
                  <span className="text-gray-2 text-base font-normal leading-7">
                    行为描述
                  </span>
                </div>
              </div>

              {/* 表格内容 */}
              <div className="bg-fill-white">
                {props.tag.detail?.map((item, index) => (
                  <div
                    key={index}
                    className={`grid grid-cols-[9.25rem_1fr] ${
                      index !== props.tag.detail.length - 1
                        ? "border-line-3 border-b"
                        : ""
                    }`}
                  >
                    {/* 时间列 */}
                    <div className="border-line-3 flex items-center justify-center border-r">
                      <span className="text-gray-1 text-center text-sm font-normal leading-6">
                        {item.timeRange}
                      </span>
                    </div>

                    {/* 描述列 */}
                    <div className="py-4.5 flex items-center px-5">
                      <span className="text-gray-2 break-words text-sm font-normal leading-6">
                        {item.behaviorDesc}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
