"use client";
import { useApp } from "@/hooks";
import DynamicRankingLeft from "@/public/icons/dynamic_ranking_left.svg";
import DynamicRankingMiddle from "@/public/icons/dynamic_ranking_middle.svg";
import DynamicRankingRight from "@/public/icons/dynamic_ranking_right.svg";
import <PERSON><PERSON><PERSON><PERSON> from "@/public/icons/ic_bell.svg";
import IcCourseSection from "@/public/icons/ic_course_section.svg";
import IcZan from "@/public/icons/ic_zan.svg";
import { praiseStudent, remindStudent } from "@/services";
import {
  GetBehaviorCategorysResponse,
  StudentBehaviorTag,
  StudentInfo,
} from "@/types/course";
import Spin from "@/ui/spin";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/ui/tabs";
import { Button } from "@/ui/tch-button";
import { toast } from "@/ui/toast";
import { umeng, Umeng<PERSON>ategory, UmengCourseAction } from "@/utils";
import { cn } from "@/utils/utils";
import { useRequest } from "ahooks";
import { useState } from "react";
import { match } from "ts-pattern";
import CourseSectionTitle from "./course-section-title";
import { DynamicRankingGoodTable } from "./dynamic-ranking-good-table";
import { DynamicRankingWarningTable } from "./dynamic-ranking-warning-table";
import EmptyDynamics from "./empty-dynamics";

export function DynamicRanking({
  classroomId,
  setStudent,
  setTag,
  isInClass,
  isSubjectTeacher,
  subject,
  studentLatestBehaviorData,
  getStudentLatestBehaviorData,
}: {
  loading: boolean;
  getStudentLatestBehaviorData: () => void;
  studentLatestBehaviorData?: GetBehaviorCategorysResponse;
  classroomId: string;
  setStudent: (student: StudentInfo) => void;
  setTag: (tag?: StudentBehaviorTag) => void;
  isInClass: boolean;
  isSubjectTeacher: boolean;
  subject: string;
}) {
  const { userInfo } = useApp();
  const [activeTab, setActiveTab] = useState<"good" | "warning" | "personal">(
    "good"
  );

  const remindStudentRequest = useRequest(
    (studentIDs: number[]) =>
      remindStudent({
        classroomID: classroomId,
        studentIDs,
        teacherID: userInfo!.userID,
        teacherName: userInfo!.userName,
      }),
    {
      manual: true,
      onSuccess: (data) => {
        toast.success(data.message);
        getStudentLatestBehaviorData();
      },
      debounceWait: 500,
    }
  );

  const praiseStudentRequest = useRequest(
    (studentIDs: number[]) =>
      praiseStudent({
        classroomID: classroomId,
        studentIDs,
        teacherID: userInfo!.userID,
        teacherName: userInfo!.userName,
      }),
    {
      manual: true,
      debounceWait: 500,
      onSuccess: (data) => {
        if (data.success) {
          toast.success(data.message);
          getStudentLatestBehaviorData();
          return;
        }

        toast.warning(data.message);
      },
    }
  );

  return (
    <section>
      <CourseSectionTitle title="课堂动态榜单" icon={IcCourseSection} />

      <Spin loading={false}>
        <Tabs
          value={activeTab}
          onValueChange={(value) => {
            setActiveTab(value as "good" | "warning");
          }}
          className={cn(
            `w-full gap-0 overflow-hidden rounded-b-lg rounded-t-[1.25rem]`
          )}
        >
          <div className="h-10">
            <div className="relative h-14">
              <DynamicRankingLeft
                className={cn(
                  `object-top-left w-238 absolute inset-0 h-14 object-none`,
                  activeTab === "good" ? "z-0" : "-z-10"
                )}
              />

              <DynamicRankingMiddle
                className={cn(
                  `object-top-left w-238 absolute inset-0 h-14 object-none`,
                  activeTab === "warning" ? "z-0" : "-z-10"
                )}
              />

              <DynamicRankingRight
                className={cn(
                  `object-top-left w-238 absolute inset-0 h-14 object-none`,
                  activeTab === "personal" ? "z-0" : "-z-10"
                )}
              />

              <TabsList
                className={cn(
                  `flex h-10 justify-between gap-6 border-0 bg-transparent p-0`
                )}
              >
                <TabsTrigger
                  value="good"
                  className={cn(
                    `bg-transparent! shadow-[unset]! text-gray-3 data-[state=active]:text-gray-2 w-54.25 relative h-full flex-none cursor-pointer gap-1 rounded-none border-0 p-0 text-base/normal font-normal data-[state=active]:font-medium`,
                    activeTab === "personal" &&
                      "before:bg-line-3 before:h-3.75 before:absolute before:-right-3 before:w-px before:content-['']",
                    activeTab === "good" && ""
                  )}
                >
                  <span className="w-0.75 bg-green-1 rounded-xs h-3.5"></span>
                  <span>
                    鼓励课堂表现（
                    {studentLatestBehaviorData?.praiseList.length || 0}人）
                  </span>
                </TabsTrigger>

                <TabsTrigger
                  value="warning"
                  className={cn(
                    `bg-transparent! shadow-[unset]! text-gray-3 data-[state=active]:text-gray-2 w-47.75 relative h-full flex-none cursor-pointer gap-1 rounded-none border-0 p-0 text-base/normal font-normal data-[state=active]:font-medium`,
                    activeTab === "warning" && ""
                  )}
                >
                  <span className="w-0.75 bg-orange-1 rounded-xs h-3.5"></span>
                  <span>
                    注意课堂表现（
                    {studentLatestBehaviorData?.attentionList.length || 0}人）
                  </span>
                </TabsTrigger>

                <TabsTrigger
                  value="personal"
                  className={cn(
                    `bg-transparent! shadow-[unset]! text-gray-3 data-[state=active]:text-gray-2 w-47.75 relative h-full flex-none cursor-pointer gap-1 rounded-none border-0 p-0 text-base/normal font-normal data-[state=active]:font-medium`,
                    activeTab === "good" &&
                      "before:bg-line-3 before:h-3.75 before:absolute before:-left-3 before:w-px before:content-['']",
                    activeTab === "personal" && ""
                  )}
                >
                  <span className="w-0.75 bg-primary-2 rounded-xs h-3.5"></span>
                  <span>
                    个人榜单
                    {/* （{studentLatestBehaviorData?.learningScores?.length || 0}人） */}
                  </span>
                </TabsTrigger>
              </TabsList>

              <div className="bottom-5.5 absolute right-0">
                {match({ isInClass, activeTab })
                  .with({ isInClass: true, activeTab: "good" }, () => {
                    return (
                      <Button
                        className="border-primary-5 bg-primary-6 text-gray-2 h-7 font-medium"
                        size="sm"
                        radius="full"
                        icon={<IcZan />}
                        disabled={praiseStudentRequest.loading}
                        onClick={() => {
                          const ids = studentLatestBehaviorData?.praiseList
                            ?.filter((s) => !s.isHandled)
                            ?.map((s) => s.studentId);

                          if (!ids?.length) {
                            return;
                          }

                          umeng.trackEvent(
                            UmengCategory.COURSE,
                            UmengCourseAction.CLASSROOM_REPORT_LIKE,
                            {
                              like_action: {
                                subject: subject,
                                job: isSubjectTeacher ? "任课教师" : "班主任",
                              },
                            }
                          );

                          praiseStudentRequest.run(ids);
                        }}
                      >
                        一键鼓励
                      </Button>
                    );
                  })
                  .with({ isInClass: true, activeTab: "warning" }, () => {
                    return (
                      <Button
                        className="border-primary-5 bg-primary-6 text-gray-2 h-7 font-medium"
                        size="sm"
                        radius="full"
                        icon={<IcBell />}
                        disabled={remindStudentRequest.loading}
                        onClick={() => {
                          const ids = studentLatestBehaviorData?.attentionList
                            ?.filter((s) => !s.isHandled)
                            ?.map((s) => s.studentId);

                          if (!ids?.length) {
                            return;
                          }

                          umeng.trackEvent(
                            UmengCategory.COURSE,
                            UmengCourseAction.CLASSROOM_REPORT_PUSH,
                            {
                              push_action: {
                                subject: subject,
                                job: isSubjectTeacher ? "任课教师" : "班主任",
                              },
                            }
                          );

                          remindStudentRequest.run(ids);
                        }}
                      >
                        一键提醒
                      </Button>
                    );
                  })
                  .otherwise(() => null)}
              </div>
            </div>
          </div>

          <div className="relative rounded-t-[1.25rem] bg-white px-5 pb-6 pt-4">
            <TabsContent value="good">
              <DynamicRankingGoodTable
                isInClass={isInClass}
                subject={subject}
                isSubjectTeacher={isSubjectTeacher}
                setStudent={setStudent}
                praiseStudentRequest={praiseStudentRequest}
                studentLatestBehaviorData={studentLatestBehaviorData}
              />
            </TabsContent>

            <TabsContent value="warning">
              <DynamicRankingWarningTable
                isInClass={isInClass}
                subject={subject}
                isSubjectTeacher={isSubjectTeacher}
                setStudent={setStudent}
                setTag={setTag}
                remindStudentRequest={remindStudentRequest}
                studentLatestBehaviorData={studentLatestBehaviorData}
              />
            </TabsContent>

            <TabsContent value="personal">
              {/* <DynamicRankingPersonalTable
                subject={subject}
                isSubjectTeacher={isSubjectTeacher}
                setStudent={setStudent}
                classroomId={classroomId}
                onPraiseSuccess={() => {
                  getStudentLatestBehaviorData();
                }}
                studentLatestBehaviorData={studentLatestBehaviorData}
              /> */}
            </TabsContent>

            {match(activeTab)
              .with(
                "good",
                () =>
                  !studentLatestBehaviorData?.praiseList?.length && (
                    <EmptyDynamics />
                  )
              )
              .with(
                "warning",
                () =>
                  !studentLatestBehaviorData?.attentionList?.length && (
                    <EmptyDynamics />
                  )
              )
              .with("personal", () => <EmptyDynamics text="敬请等待正式发布" />)
              .exhaustive()}
          </div>
        </Tabs>
      </Spin>
    </section>
  );
}
