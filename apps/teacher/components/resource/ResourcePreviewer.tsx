import ResourcePreviewerLayout, {
  ResourcePreviewerLayoutProps,
  TeacherVodPlayerTags,
} from "@repo/core/components/resource-preview/layout";

import { useApp } from "@/hooks";
import {
  getResourceCommonPreview,
  getResourceDocumentRefreshToken,
} from "@/services/resource";
import { umeng, UmengCategory } from "@/utils";
import { cn } from "@/utils/utils";
import { useRequest } from "ahooks";
import { useCallback, useRef, useState } from "react";
import { createPortal } from "react-dom";

export type ResourcePreviewerProps = Omit<
  ResourcePreviewerLayoutProps,
  | "resourcesData"
  | "isFetchingResourcesData"
  | "getResourceDocumentRefreshToken"
  | "tag"
  | "userId"
  | "subtag"
> & {
  resourceIds: string[];
  subtag: TeacherVodPlayerTags["subtag"];
};

export default function ResourcePreviewer(props: ResourcePreviewerProps) {
  const { resourceIds, closeButtonStyle, className, subtag, ...rest } = props;

  const { statusBarHeight, userInfo } = useApp();
  const userId = userInfo?.userID;

  const [documentResourceInfo, setDocumentResourceInfo] = useState<{
    accessToken: string;
    refreshToken: string;
  } | null>(null);

  const { data: resourcesData, loading: isFetchingResourcesData } = useRequest(
    async () => {
      if (!resourceIds.filter(Boolean).length) return undefined;

      const res = await Promise.all(
        resourceIds.map((resourceId) => getResourceCommonPreview(resourceId))
      );

      if (res[0]?.webOffice) {
        setDocumentResourceInfo(res[0].webOffice);
      } else {
        setDocumentResourceInfo(null);
      }

      return res;
    },
    {
      refreshDeps: [resourceIds.join(",")],
      ready: !!resourceIds.filter(Boolean).length,
      cacheKey: `resource-previewer-${resourceIds.join(",")}`,
    }
  );

  const { runAsync } = useRequest(
    async () => {
      if (!documentResourceInfo) throw new Error("No document resource info");

      const res = await getResourceDocumentRefreshToken(
        documentResourceInfo.accessToken,
        documentResourceInfo.refreshToken
      );

      setDocumentResourceInfo({
        ...documentResourceInfo,
        ...res,
      });

      return {
        token: res.accessToken,
        timeout:
          Date.parse(res.accessTokenExpiredTime) - Date.now() - 5 * 60 * 1000,
      };
    },
    {
      manual: true,
      ready: documentResourceInfo?.refreshToken !== undefined,
      cacheKey: `resource-previewer-${documentResourceInfo?.accessToken}`,
    }
  );

  const resourceIdsRef = useRef(resourceIds);
  resourceIdsRef.current = resourceIds;
  const onDocPreviewHyperLinkOpen = useCallback((linkUrl: string) => {
    umeng.trackEvent(
      UmengCategory.COMMON,
      "teacher_doc_preview_hyper_link_open",
      {
        resource_id: resourceIdsRef.current?.[0],
        url: linkUrl,
      }
    );
  }, []);

  return createPortal(
    <ResourcePreviewerLayout
      closeButtonStyle={{
        ...closeButtonStyle,
        top: `${statusBarHeight + 8}px`,
      }}
      className={cn("z-51 pointer-events-auto fixed left-0 top-0", className)}
      {...rest}
      tag="教师端"
      subtag={subtag}
      userId={userId?.toString()}
      resourcesData={resourcesData}
      isFetchingResourcesData={isFetchingResourcesData}
      getResourceDocumentRefreshToken={runAsync}
      onDocPreviewHyperLinkOpen={onDocPreviewHyperLinkOpen}
    />,
    document.body
  );
}
