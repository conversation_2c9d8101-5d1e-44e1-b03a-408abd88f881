"use client";

import { Checkbox } from "@/ui/checkbox";
import { cn } from "@/utils";
import { ChevronDownIcon } from "lucide-react";
import * as React from "react";
import Select, { type GroupBase, OptionProps, components } from "react-select";

function OptionComponent<
  Option,
  IsMulti extends boolean = false,
  Group extends GroupBase<Option> = GroupBase<Option>,
>({ children, ...props }: OptionProps<Option, IsMulti, Group>) {
  return (
    <components.Option {...props} className="bg-white">
      <div className="hover:bg-primary-6 flex w-full cursor-pointer items-center gap-1.5 px-4 py-3 transition-colors">
        <Checkbox
          checked={props.isSelected}
          className="rounded-0.75 border-px size-4 border-[#cfd5e8] data-[state=checked]:border-[#6574fc]"
          tabIndex={-1}
        />
        <div className="text-gray-2 whitespace-nowrap text-sm/normal">
          {children}
        </div>
      </div>
    </components.Option>
  );
}

export function TchSelect<
  Option,
  IsMulti extends boolean = false,
  Group extends GroupBase<Option> = GroupBase<Option>,
>({
  classNames,
  ...props
}: React.ComponentProps<typeof Select<Option, IsMulti, Group>>) {
  return (
    <Select
      isSearchable={false}
      isClearable={false}
      hideSelectedOptions={false}
      unstyled
      closeMenuOnSelect={!props.isMulti}
      components={{
        DropdownIndicator: (props) => {
          return (
            <components.DropdownIndicator {...props}>
              <ChevronDownIcon className="text-gray-2 size-3.5" />
            </components.DropdownIndicator>
          );
        },
        Option: OptionComponent<Option, IsMulti, Group>,
      }}
      {...props}
      classNames={{
        // 控制器样式 - 符合设计稿规范
        control: (payload) =>
          cn(
            "border-line-1 cursor-pointer! flex w-full px-4! min-h-8! gap-1.5 items-center justify-between rounded-[4.5rem] border bg-white text-sm transition-colors",
            payload.isDisabled && "cursor-not-allowed",
            // 外部传入的样式覆盖
            classNames?.control?.(payload)
          ),

        // 值容器
        valueContainer: (state) =>
          cn("flex flex-wrap gap-2 p-0", classNames?.valueContainer?.(state)),

        // 输入框
        input: (state) =>
          cn(
            "text-gray-2 outline-none", // 使用设计系统的 gray-2 颜色
            classNames?.input?.(state)
          ),

        // 占位符
        placeholder: (state) =>
          cn("text-gray-2", classNames?.placeholder?.(state)),

        // 单选值
        singleValue: (state) =>
          cn(
            "text-gray-2", // 使用设计系统的 gray-2 颜色
            classNames?.singleValue?.(state)
          ),

        // 多选值容器
        multiValue: (state) =>
          cn(
            "inline-flex items-center gap-0.5 text-gray-2 bg-fill-gray-2 px-2 rounded-full",
            classNames?.multiValue?.(state)
          ),

        // 多选值标签
        multiValueLabel: (state) =>
          cn("", classNames?.multiValueLabel?.(state)),

        // 多选值删除按钮
        multiValueRemove: (state) =>
          cn(
            " hover:text-destructive ml-1 rounded-sm hover:bg-destructive/20",
            classNames?.multiValueRemove?.(state)
          ),

        // 指示器容器
        indicatorsContainer: (state) =>
          cn("flex items-center", classNames?.indicatorsContainer?.(state)),

        // 指示器分隔符
        indicatorSeparator: (state) =>
          cn("hidden", classNames?.indicatorSeparator?.(state)),

        // 下拉指示器
        dropdownIndicator: (state) =>
          cn(classNames?.dropdownIndicator?.(state)),

        // 清除指示器
        clearIndicator: (state) =>
          cn(
            "text-muted-foreground hover:text-foreground p-1",
            classNames?.clearIndicator?.(state)
          ),

        // 菜单容器
        menu: (state) =>
          cn(
            "bg-popover text-popover-foreground z-50 w-[unset]! min-w-full rounded-xl border shadow-[0px_1rem_3.5rem_0px_rgba(16,18,25,0.08)] mt-1 overflow-hidden",
            classNames?.menu?.(state)
          ),

        // 菜单列表
        menuList: (state) =>
          cn("p-1 max-h-60 overflow-auto", classNames?.menuList?.(state)),

        // 选项
        option: (payload) =>
          cn(
            // 基础样式
            "flex w-full cursor-default select-none items-center rounded-lg text-sm outline-none",
            // 聚焦样式
            payload.isFocused &&
              "bg-accent text-accent-foreground cursor-pointer",
            // 选中样式
            payload.isSelected &&
              "bg-primary text-primary-foreground font-medium",
            // 禁用样式
            payload.isDisabled && "pointer-events-none opacity-50",
            // 外部传入的样式覆盖
            classNames?.option?.(payload)
          ),

        // 无选项消息
        noOptionsMessage: (state) =>
          cn(
            "text-muted-foreground p-4 text-center text-sm",
            classNames?.noOptionsMessage?.(state)
          ),

        // 加载消息
        loadingMessage: (state) =>
          cn(
            "text-muted-foreground p-4 text-center text-sm",
            classNames?.loadingMessage?.(state)
          ),

        // 分组
        group: (state) => cn("", classNames?.group?.(state)),

        // 分组标题
        groupHeading: (state) =>
          cn(
            "text-muted-foreground px-4 py-2 text-xs font-medium uppercase tracking-wide",
            classNames?.groupHeading?.(state)
          ),
      }}
    />
  );
}

// 导出默认组件
export default TchSelect;
