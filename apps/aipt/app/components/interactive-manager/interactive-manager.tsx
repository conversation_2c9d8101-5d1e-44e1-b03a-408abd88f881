"use client";
import { <PERSON><PERSON> } from "@/app/components/common/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from "@/app/components/ui/dialog";
import { Input } from "@/app/components/ui/input";
import { useGuideContext } from "@/app/context/guide-context";
import { useInteractiveActions } from "@/app/hooks/use-interactive-actions";
import { useInteractiveDragDrop } from "@/app/hooks/use-interactive-drag-drop";
import { useInteractiveFileUpload } from "@/app/hooks/use-interactive-file-upload";
import { BaseWidget } from "@/types/guide-widget";
import {
  FILE_UPLOAD_CONFIG,
  InteractiveData,
  InteractiveManagerProps,
} from "@/types/interactive";
import InteractiveComponent from "@repo/core/components/interactive-component";
import { Copy, GripVertical, Plus, Upload } from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { InteractivePreviewModal } from "./interactive-preview-modal";

export const InteractiveManager = ({
  onOrderChange,
  onListChange,
  baseWidgetList,
  guideWidgetId,
  guideWidgetSetId,
  onRefresh,
}: InteractiveManagerProps) => {
  // 获取全局状态
  const { isFinish, isGenerating } = useGuideContext();
  const editDisabled = isFinish || isGenerating;

  // 将 BaseWidget 转换为 InteractiveData 格式
  const convertBaseWidgetToInteractiveData = useCallback(
    (widgets: BaseWidget[]): InteractiveData[] => {
      return widgets.map((widget, index) => ({
        id: widget.baseWidgetId,
        url: widget.data.url,
        typeName: widget.data.typeName,
        index: index,
        widgetIndex: index + 1,
        widgetName: widget.widgetName || `互动${index + 1}`,
        sourceType: widget.widgetOri === "ai" ? "ai_generated" : "upload",
      }));
    },
    []
  );

  // 初始化数据
  const initialData = useMemo(
    () =>
      baseWidgetList && baseWidgetList.length > 0
        ? convertBaseWidgetToInteractiveData(baseWidgetList)
        : [],
    [baseWidgetList, convertBaseWidgetToInteractiveData]
  );

  const [interactiveList, setInteractiveList] =
    useState<InteractiveData[]>(initialData);
  const [renderKey, setRenderKey] = useState<number>(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 使用自定义 hooks
  const { handleFileUpload, handleFileReplace, handleCreateEmpty } =
    useInteractiveFileUpload({
      guideWidgetId,
      guideWidgetSetId,
      onRefresh,
      interactiveList,
      setInteractiveList,
    });

  const {
    draggedItem,
    dragOverIndex,
    handleDragStart,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    handleDragEnd,
  } = useInteractiveDragDrop({
    interactiveList,
    setInteractiveList,
    setRenderKey,
    onOrderChange,
  });

  const {
    previewModal,
    deleteModal,
    handlePreview,
    handleClosePreview,
    handleDelete,
    handleCloseDelete,
    confirmDelete,
    handleCopyUrl,
  } = useInteractiveActions({
    guideWidgetId,
    guideWidgetSetId,
    onRefresh,
    interactiveList,
    setInteractiveList,
  });

  // 当 baseWidgetList 变化时，更新组件列表
  useEffect(() => {
    if (baseWidgetList) {
      const convertedData = convertBaseWidgetToInteractiveData(baseWidgetList);
      setInteractiveList(convertedData);
    }
  }, [baseWidgetList, convertBaseWidgetToInteractiveData]);

  // 同步列表变化到父组件
  useEffect(() => {
    onListChange?.(interactiveList);
  }, [interactiveList, onListChange]);

  // 处理文件选择
  const handleFileChange = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      await handleFileUpload(file);

      // 清空文件输入
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    },
    [handleFileUpload]
  );

  // 处理替换文件
  const handleReplaceClick = useCallback(
    (id: string) => {
      const input = document.createElement("input");
      input.type = "file";
      input.accept = ".js,.html";
      input.onchange = async (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (file) {
          await handleFileReplace(id, file);
        }
      };
      input.click();
    },
    [handleFileReplace]
  );

  // 渲染预览图组件 - 使用 useCallback 优化
  const renderPreview = useCallback(
    (item: InteractiveData) => {
      // 如果是空组件，显示占位符
      if (!item.url || !item.typeName) {
        return (
          <div className="flex h-full w-full items-center justify-center bg-gray-50">
            <div className="text-center">
              <div className="mb-1 text-xs font-medium text-gray-400">
                📦 空组件
              </div>
              <div className="text-[10px] text-gray-400">
                请上传JS或HTML文件
              </div>
            </div>
          </div>
        );
      }

      // 检测文件类型
      const isHtmlFile = item.url && item.url.toLowerCase().endsWith(".html");

      if (isHtmlFile) {
        // HTML文件使用iframe渲染，复用分栏编辑的渲染逻辑
        return (
          <div className="relative h-full w-full overflow-hidden">
            <iframe
              src={item.url}
              className="absolute inset-0 border-0"
              title="HTML预览"
              sandbox="allow-scripts allow-same-origin"
              style={{
                width: "400%",
                height: "400%",
                transform: "scale(0.25)",
                transformOrigin: "0 0",
                overflow: "hidden",
              }}
            />
          </div>
        );
      } else {
        // JS文件使用InteractiveComponent渲染
        return (
          <div className="relative h-full w-full overflow-hidden">
            <div
              className="absolute inset-0"
              style={{
                transform: "scale(0.25)",
                transformOrigin: "center center",
                width: "400%",
                height: "400%",
                left: "-150%",
                top: "-150%",
              }}
            >
              <InteractiveComponent
                key={`${item.id}-${item.url}-${item.typeName}-${renderKey}`}
                url={item.url}
                type={item.typeName}
                onReport={() => {}}
                onLoad={() => {}}
                onError={() => {}}
                className="h-full w-full"
              >
                <div className="flex h-full items-center justify-center">
                  <div className="text-center">
                    <div className="mb-1 text-xs font-medium text-blue-600">
                      ⚡ JS组件
                    </div>
                    <div className="text-[10px] text-gray-500">
                      {item.typeName}
                    </div>
                  </div>
                </div>
              </InteractiveComponent>
            </div>
          </div>
        );
      }
    },
    [renderKey]
  );

  // 空状态
  if (interactiveList.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="text-center">
          <div className="mb-4">
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
          </div>
          <h3 className="mb-2 text-sm font-medium text-gray-900">
            暂无互动组件
          </h3>
          <p className="mb-4 text-sm text-gray-500">
            可以上传JS或HTML文件，或创建空组件后再添加内容
          </p>
          <div className="flex gap-2">
            <Button
              type="outline"
              onClick={() => fileInputRef.current?.click()}
              disabled={editDisabled}
              icon={<Plus className="h-4 w-4" />}
            >
              添加互动
            </Button>
            <Button
              type="outline"
              onClick={handleCreateEmpty}
              disabled={editDisabled}
              icon={<Plus className="h-4 w-4" />}
            >
              创建空组件
            </Button>
          </div>
          <Input
            ref={fileInputRef}
            type="file"
            accept=".js,.html"
            onChange={handleFileChange}
            className="hidden"
          />
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-5">
        {interactiveList.map((item, index) => (
          <div
            key={item.id}
            className={`group flex items-center gap-5 rounded-xl border bg-white p-5 shadow-sm transition-all duration-300 ${
              editDisabled ? "opacity-60" : "hover:shadow-md"
            } ${draggedItem === item.id ? "scale-95 opacity-50" : ""} ${
              dragOverIndex === index
                ? "border-blue-400 bg-blue-50 shadow-lg"
                : "border-gray-200 hover:border-gray-300"
            }`}
            draggable={!editDisabled}
            onDragStart={(e) => !editDisabled && handleDragStart(e, item.id)}
            onDragOver={(e) => !editDisabled && handleDragOver(e, index)}
            onDragLeave={!editDisabled ? handleDragLeave : undefined}
            onDrop={(e) => !editDisabled && handleDrop(e, index)}
            onDragEnd={!editDisabled ? handleDragEnd : undefined}
          >
            {/* 拖拽手柄 */}
            <div className="flex-shrink-0 cursor-grab opacity-50 transition-opacity duration-200 active:cursor-grabbing group-hover:opacity-80">
              <GripVertical className="h-5 w-5 text-gray-400" />
            </div>

            {/* 组件预览图区域 */}
            <div className="flex-shrink-0">
              <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-md">
                <div className="flex h-24 w-40 items-center justify-center">
                  {renderPreview(item)}
                </div>
              </div>
            </div>

            {/* 组件信息区域 */}
            <div className="flex-1">
              <div className="space-y-3">
                {/* 组件名称和状态 */}
                <div className="flex items-center gap-3">
                  <h3 className="text-lg font-semibold text-gray-800">
                    {item.widgetName || `互动${index + 1}`}
                  </h3>
                  <span
                    className={`rounded-full px-3 py-1 text-xs font-medium ${
                      item.sourceType === "upload"
                        ? "bg-green-100 text-green-600"
                        : item.sourceType === "ai_generated"
                          ? "bg-purple-100 text-purple-600"
                          : "bg-blue-100 text-blue-600"
                    }`}
                  >
                    {item.sourceType === "upload"
                      ? `本地上传${item.url && item.url.toLowerCase().endsWith(".html") ? "(HTML)" : "(JS)"}`
                      : item.sourceType === "ai_generated"
                        ? "AI生成"
                        : "模板组件"}
                  </span>
                </div>

                {/* URL显示 - 优化版本 */}
                <div className="flex items-center">
                  <div className="w-16 flex-shrink-0 text-sm text-gray-600">
                    URL：
                  </div>
                  <div className="flex flex-1 items-center gap-2">
                    <div className="max-w-xs flex-1 truncate text-xs text-gray-500">
                      {item.url || "暂未设置"}
                    </div>
                    {item.url && (
                      <Button
                        type="text"
                        size="sm"
                        onClick={() => handleCopyUrl(item.url)}
                        className="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600"
                        title="复制URL"
                      >
                        <Copy className="h-3.5 w-3.5" />
                      </Button>
                    )}
                  </div>
                </div>

                {/* 最近更新时间 */}
                <div className="flex items-center">
                  <div className="w-16 flex-shrink-0 text-sm text-gray-600">
                    更新：
                  </div>
                  <div className="flex-1 text-xs text-gray-500">
                    {new Date().toLocaleString("zh-CN", {
                      month: "2-digit",
                      day: "2-digit",
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </div>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex flex-col gap-2">
              <Button
                type="outline"
                size="sm"
                onClick={() => handlePreview(item)}
                className="min-w-[68px] transition-transform duration-200 hover:scale-105"
              >
                预览
              </Button>
              <Button
                type="outline"
                size="sm"
                onClick={() => handleReplaceClick(item.id)}
                disabled={editDisabled}
                className="min-w-[68px] transition-transform duration-200 hover:scale-105"
              >
                替换
              </Button>
              <Button
                type="outline"
                size="sm"
                onClick={() => handleDelete(item.id)}
                disabled={editDisabled}
                className="min-w-[68px] border-red-200 text-red-600 transition-all duration-200 hover:scale-105 hover:border-red-300 hover:bg-red-50"
              >
                删除
              </Button>
            </div>
          </div>
        ))}

        {interactiveList.length < FILE_UPLOAD_CONFIG.MAX_COMPONENTS && (
          <div className="flex justify-center gap-2 pt-4">
            <Button
              type="outline"
              onClick={() => fileInputRef.current?.click()}
              disabled={editDisabled}
              icon={<Plus className="h-4 w-4" />}
            >
              添加互动
            </Button>
            <Button
              type="outline"
              onClick={handleCreateEmpty}
              disabled={editDisabled}
              icon={<Plus className="h-4 w-4" />}
            >
              创建空组件
            </Button>
            <Input
              ref={fileInputRef}
              type="file"
              accept=".js,.html"
              onChange={handleFileChange}
              className="hidden"
            />
          </div>
        )}

        {/* 拖拽提示 */}
        {draggedItem && (
          <div className="fixed bottom-4 right-4 rounded-lg bg-blue-600 px-4 py-2 text-white shadow-lg">
            拖拽到目标位置进行排序
          </div>
        )}
      </div>

      {/* 预览模态框 - 支持真实组件渲染 */}
      <InteractivePreviewModal
        open={previewModal.open}
        onClose={handleClosePreview}
        url={previewModal.data?.url || ""}
        typeName={previewModal.data?.typeName || ""}
        fileType={
          previewModal.data?.url &&
          previewModal.data.url.toLowerCase().endsWith(".html")
            ? "html"
            : "js"
        }
      />

      {/* 删除确认弹框 */}
      <Dialog open={deleteModal.open} onOpenChange={handleCloseDelete}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              确定要删除互动组件 &quot;
              {deleteModal.data
                ? deleteModal.data.widgetName ||
                  `互动${interactiveList.findIndex((item) => item.id === deleteModal.data?.id) + 1}`
                : ""}
              &quot; 吗？
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-2">
            <Button type="outline" onClick={handleCloseDelete}>
              取消
            </Button>
            <Button
              onClick={confirmDelete}
              className="bg-red-600 text-white hover:bg-red-700"
            >
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
