"use client";
import { But<PERSON> } from "@/app/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/app/components/ui/dialog";
import { Input } from "@/app/components/ui/input";
import emitter from "@/lib/emitter";
import { post } from "@/lib/fetcher";
import { uploadFile } from "@/lib/uploader";
import DeleteImg from "@/public/delete.svg";
import { Modal } from "antd";
import { cloneDeep } from "lodash";
import { useSearchParams } from "next/navigation";
import { useRef, useState } from "react";
import { toast } from "sonner";
import { useLessonFlow } from "../_context/lesson-flow-context";

interface EditInteractiveProps {
  onSubmit?: () => void;
  setOpen: (open: boolean) => void;
  [key: string]: any;
}

const initInfo = {
  url: "",
  typeName: "",
};

const getWebComponentName = (fileContent: string, fileName: string) => {
  // 如果是HTML文件，从title或h1标签提取组件名，或使用文件名生成
  if (fileName.toLowerCase().endsWith(".html")) {
    const titleMatch = fileContent.match(/<title[^>]*>([^<]+)<\/title>/i);
    const h1Match = fileContent.match(/<h1[^>]*>([^<]+)<\/h1>/i);

    let extractedName = "";
    if (titleMatch && titleMatch[1]) {
      extractedName = titleMatch[1].trim();
    } else if (h1Match && h1Match[1]) {
      extractedName = h1Match[1].trim();
    }

    if (extractedName) {
      // 将提取的标题转换为有效的组件名
      let name = extractedName.toLowerCase();
      name = name.replace(/[^a-z0-9]/g, "-");
      name = name.replace(/-+/g, "-");
      name = name.replace(/^-+|-+$/g, "");

      if (!name.includes("-")) {
        name = "html-" + name;
      }

      if (/^[0-9]/.test(name)) {
        name = "component-" + name;
      }

      return name || `html-component-${Date.now()}`;
    }

    // 如果没有找到有效标题，使用文件名生成
    let name = fileName.replace(/\.html$/i, "").toLowerCase();
    name = name.replace(/[^a-z0-9]/g, "-");
    name = name.replace(/-+/g, "-");
    name = name.replace(/^-+|-+$/g, "");

    if (!name.includes("-")) {
      name = "html-" + name;
    }

    return name || `html-component-${Date.now()}`;
  }

  // 对于JS文件，使用原有逻辑
  const regex = /customElements\.define\s*\(\s*["']([^"']+)["']/;
  const match = fileContent.match(regex);
  if (match && match[1]) {
    return match[1].trim();
  }
};

const EditInteractive: React.FC<EditInteractiveProps> = (props) => {
  const { interactiveNodes } = useLessonFlow();
  const { onSubmit, setOpen } = props;
  const inputDomRef = useRef<HTMLInputElement>(null);
  const searchParams = useSearchParams();
  const lessonId = Number(searchParams.get("lessonId")); // 获取查询参数
  const [modal, contextHolder] = Modal.useModal();
  const [info, setInfo] = useState(
    props.data.interactive ?? cloneDeep(initInfo)
  );

  const infoRef = useRef(props.data.interactive ?? cloneDeep(initInfo));
  const isLoading = useRef(false);

  const hidden = props.data.hidden || 0; // 0显示 1隐藏
  const updateExercise = async () => {
    return post("/api/v1/lesson_widget/update/interactive", {
      arg: {
        lessonId,
        widgetIndex: props.data.index,
        interactiveInfo: infoRef.current,
        hidden,
      },
    });
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      // 检查文件类型
      const isJsFile = file.name.endsWith(".js");
      const isHtmlFile = file.name.endsWith(".html");

      if (!isJsFile && !isHtmlFile) {
        toast.error("请上传.js或.html文件");
        inputDomRef.current!.value = "";
        return;
      }

      // 上传文件前，检查是否存在同名组件
      const fileContent = await file.text();
      const compName = getWebComponentName(fileContent, file.name);
      if (!compName) {
        toast.error("无法检测到互动组件名称，请上传正确的互动组件文件");
        inputDomRef.current!.value = "";
        return;
      }
      if (
        interactiveNodes.find(
          (item: any) => item?.data?.interactive?.typeName === compName
        )
      ) {
        const res = await modal.confirm({
          content: `当前上传互动组件名称: ${compName} 已经存在，如果组件内容和之前一致，忽略本消息；否则请修改组件名称后重新上传。`,
          okText: "忽略",
          cancelText: "重新上传",
        });
        if (!res) {
          inputDomRef.current!.value = "";
          return;
        }
      }
      const { url } = await uploadFile(file, "2");
      setInfo({ ...infoRef.current, url });
      infoRef.current = { ...infoRef.current, url };
    }
  };

  const onSubmitHandle = async () => {
    if (isLoading.current) return;
    try {
      const strContent = await fetch(info.url).then((res) => res.text());
      // 从URL中提取文件名来判断文件类型
      const urlParts = info.url.split("/");
      const fileName = urlParts[urlParts.length - 1] || "component.js";
      const compName = getWebComponentName(strContent, fileName);
      if (compName) {
        const componentName = compName;
        setInfo({ ...info, typeName: componentName });
        infoRef.current = { ...infoRef.current, typeName: componentName };
      } else {
        toast.error("请输入正确的互动组件地址");
        return;
      }
      try {
        isLoading.current = true;
        await updateExercise();
        emitter.emit("refresh");
        setOpen(false);
        emitter.emit("openInteractiveSheet", {
          ...props,
          lessonId,
          interactiveInfo: info,
          hidden: hidden,
        });
      } catch (error: any) {
        toast.error(error.message);
      } finally {
        isLoading.current = false;
      }
    } catch (error) {
      toast.error("请输入正确的互动组件地址");
      console.log(error);
      return;
    }
  };
  return (
    <>
      <div className="insert-node-modal">
        <div className="mt-4">
          <div className="items-center gap-2">
            互动组件：
            <div className="flex items-center gap-2 break-all text-sm">
              {info.url ? (
                <>
                  <span className="wrap-break-word flex-1 rounded-md bg-gray-100 p-2 text-sm text-gray-500">
                    {info.url}
                  </span>
                  <div>
                    <DeleteImg
                      width={18}
                      height={18}
                      className="cursor-pointer text-red-500 opacity-50"
                      onClick={() => {
                        setInfo({ ...info, url: "" });
                        infoRef.current = { ...info, url: "" };
                      }}
                    />
                  </div>
                </>
              ) : (
                <Input
                  ref={inputDomRef}
                  className="flex-1"
                  type="file"
                  onChange={handleFileChange}
                  accept=".js,.html"
                />
              )}
            </div>
          </div>
        </div>
        <div className="mt-6 flex justify-end gap-10">
          <DialogClose asChild>
            <Button variant="outline">取消</Button>
          </DialogClose>
          <Button onClick={onSubmitHandle}>提交</Button>
        </div>
      </div>
      <div>{contextHolder}</div>
    </>
  );
};

const EditInteractiveModal = (props: any) => {
  return (
    <Dialog open={props.open} onOpenChange={props.setOpen}>
      <DialogContent
        onInteractOutside={(e) => e.preventDefault()}
        className="bg-white"
      >
        <DialogHeader>
          <DialogTitle>编辑互动组件</DialogTitle>
        </DialogHeader>
        <EditInteractive {...props} setOpen={props.setOpen} />
      </DialogContent>
    </Dialog>
  );
};

export default EditInteractiveModal;
