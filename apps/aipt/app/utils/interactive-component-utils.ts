/**
 * 互动组件工具函数
 * 提供组件名称处理、验证等通用功能
 */

/**
 * 从JS或HTML文件中提取组件名称
 * @param file 要解析的JS或HTML文件
 * @returns Promise<string> 提取的组件名称
 */
export const extractComponentName = async (file: File): Promise<string> => {
  return new Promise((resolve) => {
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const fileExtension = file.name.toLowerCase().split(".").pop();

        if (fileExtension === "html") {
          // 对于HTML文件，尝试提取title或生成基于文件名的组件名
          const titleMatch = content.match(/<title[^>]*>([^<]+)<\/title>/i);
          const h1Match = content.match(/<h1[^>]*>([^<]+)<\/h1>/i);

          let extractedName = "";
          if (titleMatch && titleMatch[1]) {
            extractedName = titleMatch[1].trim();
          } else if (h1Match && h1Match[1]) {
            extractedName = h1Match[1].trim();
          }

          if (extractedName) {
            // 将提取的标题转换为有效的组件名
            const validName = generateValidComponentNameFromText(extractedName);
            if (isValidCustomElementName(validName)) {
              resolve(validName);
              return;
            }
          }

          // 如果没有找到有效标题，使用文件名生成
          resolve(generateValidComponentName(file.name));
        } else {
          // 对于JS文件，使用原有逻辑
          const regex = /customElements\.define\(['"`]([^'"`]+)['"`]/;
          const match = content.match(regex);

          if (match && match[1]) {
            // 验证从JS文件中提取的组件名是否符合HTML自定义元素规范
            const extractedName = match[1];
            if (isValidCustomElementName(extractedName)) {
              resolve(extractedName);
            } else {
              // 如果提取的名称无效，则使用文件名生成有效名称
              resolve(generateValidComponentName(file.name));
            }
          } else {
            // 如果没有找到customElements.define，则从文件名生成有效名称
            resolve(generateValidComponentName(file.name));
          }
        }
      } catch (error) {
        console.error("解析组件名称失败:", error);
        // 出错时使用文件名生成有效名称
        resolve(generateValidComponentName(file.name));
      }
    };

    reader.onerror = (error) => {
      console.error("读取文件失败:", error);
      // 读取失败时使用文件名生成有效名称
      resolve(generateValidComponentName(file.name));
    };

    reader.readAsText(file);
  });
};

/**
 * 验证是否为有效的HTML自定义元素名称
 * @param name 要验证的名称
 * @returns boolean 是否有效
 */
export const isValidCustomElementName = (name: string): boolean => {
  // HTML自定义元素名称规则：
  // 1. 必须包含连字符
  // 2. 只能包含小写字母、数字和连字符
  // 3. 不能以数字开头
  // 4. 不能以连字符开头或结尾
  const validNameRegex = /^[a-z][a-z0-9]*(-[a-z0-9]+)+$/;
  return validNameRegex.test(name);
};

/**
 * 从文本内容生成有效的组件名称
 * @param text 原始文本内容
 * @returns string 生成的有效组件名称
 */
export const generateValidComponentNameFromText = (text: string): string => {
  // 转换为小写
  let name = text.toLowerCase();

  // 替换所有非字母数字字符为连字符
  name = name.replace(/[^a-z0-9]/g, "-");

  // 移除连续的连字符
  name = name.replace(/-+/g, "-");

  // 移除开头和结尾的连字符
  name = name.replace(/^-+|-+$/g, "");

  // 确保名称包含连字符（HTML自定义元素要求）
  if (!name.includes("-")) {
    name = "html-" + name;
  }

  // 确保不以数字开头
  if (/^[0-9]/.test(name)) {
    name = "component-" + name;
  }

  // 如果名称为空或无效，使用默认名称
  if (!name || !isValidCustomElementName(name)) {
    name = "html-component-" + Date.now();
  }

  return name;
};

/**
 * 从文件名生成有效的组件名称
 * @param fileName 原始文件名
 * @returns string 生成的有效组件名称
 */
export const generateValidComponentName = (fileName: string): string => {
  // 移除文件扩展名
  let name = fileName.replace(/\.(js|html)$/i, "");

  // 转换为小写
  name = name.toLowerCase();

  // 替换所有非字母数字字符为连字符
  name = name.replace(/[^a-z0-9]/g, "-");

  // 移除连续的连字符
  name = name.replace(/-+/g, "-");

  // 移除开头和结尾的连字符
  name = name.replace(/^-+|-+$/g, "");

  // 确保名称包含连字符（HTML自定义元素要求）
  if (!name.includes("-")) {
    // 根据原始文件类型添加前缀
    const isHtml = fileName.toLowerCase().endsWith(".html");
    name = (isHtml ? "html-" : "custom-") + name;
  }

  // 确保不以数字开头
  if (/^[0-9]/.test(name)) {
    name = "component-" + name;
  }

  // 如果名称为空或无效，使用默认名称
  if (!name || !isValidCustomElementName(name)) {
    const isHtml = fileName.toLowerCase().endsWith(".html");
    name = (isHtml ? "html-component-" : "interactive-component-") + Date.now();
  }

  return name;
};
