import FingerprintJS from '@fingerprintjs/fingerprintjs';
import md5 from 'md5';

interface FingerprintOptions {
  debug?: boolean;
  ttl?: number;
  threshold?: number;
}

interface FingerprintData {
  visitorId: string;
  timestamp: number;
  userAgent: string;
  language: string;
  colorDepth: number;
  deviceMemory: number;
  hardwareConcurrency: number;
  timezone: string;
  plugins: string[];
  canvas: string;
}

interface CompactFingerprint {
  id: string; // visitorId
  hw: string; // 硬件特征，格式: "cores|memory|colorDepth"
  env: string; // 环境特征，格式: "language|timezone"
  cv: string; // canvas哈希的前8位
}

interface CacheData {
  fp: string;
  timestamp: number;
  data: FingerprintData;
}

const DEFAULT_OPTIONS: FingerprintOptions = {
  debug: false,
  ttl: 3600,
  threshold: 0.65,
};

const CACHE_KEY = 'fingerprint_cache';
const FP_MAP_PREFIX = 'fp_map_';

// 调整权重，减少可能受域名影响的因素权重
const WEIGHTS = {
  visitorId: 0.25, // 增加基础指纹权重
  canvas: 0.45, // 增加canvas权重
  hardwareConcurrency: 0.1,
  deviceMemory: 0.01,
  colorDepth: 0.05,
  language: 0.02, // 降低语言权重
  timezone: 0.02, // 降低时区权重
  userAgent: 0.1, // 降低userAgent权重
  plugins: 0.0, // 移除插件权重，因为插件列表可能因域名而异
};

// 规范化 UserAgent
const normalizeUserAgent = (ua: string): string => {
  return ua
    .replace(/\([^)]*\)/g, '') // 移除括号内容（通常包含域名相关信息）
    .replace(/\s+/g, ' ') // 规范化空格
    .replace(/https?:\/\/[^\s]+/g, '') // 移除URL
    .replace(/[0-9]+\.[0-9]+\.[0-9]+/g, 'x.x.x') // 统一版本号格式
    .trim() // 移除首尾空格
    .toLowerCase(); // 转小写
};

// 过滤插件列表，移除域名相关插件
const filterPlugins = (plugins: string[]): string[] => {
  return plugins.filter((p) => {
    const lowerP = p.toLowerCase();
    return !lowerP.includes('domain') && !lowerP.includes('site') && !lowerP.includes('host');
  });
};

const getCanvasFingerprint = (): string => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (!ctx) return '';

  canvas.width = 200;
  canvas.height = 50;
  ctx.textBaseline = 'top';
  ctx.font = '14px Arial';
  ctx.fillStyle = '#f60';
  ctx.fillRect(10, 10, 100, 30);
  ctx.fillStyle = '#069';
  ctx.fillText('Browser Fingerprint', 2, 2);

  return canvas.toDataURL();
};

const calculateSimilarity = (fp1: FingerprintData, fp2: FingerprintData): number => {
  let totalScore = 0;

  // 基础指纹ID比较
  if (fp1.visitorId === fp2.visitorId) totalScore += WEIGHTS.visitorId;

  // Canvas指纹比较
  if (fp1.canvas === fp2.canvas) totalScore += WEIGHTS.canvas;

  // 硬件特征比较
  if (fp1.hardwareConcurrency === fp2.hardwareConcurrency)
    totalScore += WEIGHTS.hardwareConcurrency;
  if (fp1.deviceMemory === fp2.deviceMemory) totalScore += WEIGHTS.deviceMemory;
  if (fp1.colorDepth === fp2.colorDepth) totalScore += WEIGHTS.colorDepth;

  // 语言和时区比较
  if (fp1.language === fp2.language) totalScore += WEIGHTS.language;
  if (fp1.timezone === fp2.timezone) totalScore += WEIGHTS.timezone;

  // UserAgent比较（规范化后）
  const ua1 = normalizeUserAgent(fp1.userAgent);
  const ua2 = normalizeUserAgent(fp2.userAgent);
  if (ua1 === ua2) totalScore += WEIGHTS.userAgent;

  // 插件列表比较（过滤后）
  const plugins1 = filterPlugins(fp1.plugins);
  const plugins2 = filterPlugins(fp2.plugins);
  const pluginsSimilarity =
    plugins1.filter((p) => plugins2.includes(p)).length /
    Math.max(plugins1.length, plugins2.length);
  totalScore += pluginsSimilarity * WEIGHTS.plugins;

  return totalScore;
};

// 创建紧凑格式的指纹数据
const createCompactFingerprint = (data: FingerprintData): CompactFingerprint => {
  return {
    id: data.visitorId,
    hw: `${data.hardwareConcurrency}|${data.deviceMemory}|${data.colorDepth}`,
    env: `${data.language}|${data.timezone}`,
    cv: md5(data.canvas).substring(0, 8),
  };
};

// 从紧凑格式创建指纹数据对象
const expandCompactFingerprint = (compact: CompactFingerprint): FingerprintData => {
  const [hw1, hw2, hw3] = (compact.hw || '0|0|0').split('|');
  const [lang, tz] = (compact.env || '|').split('|');

  return {
    visitorId: compact.id || '',
    timestamp: 0,
    userAgent: '',
    language: lang || '',
    colorDepth: parseInt(hw3) || 0,
    deviceMemory: parseInt(hw2) || 0,
    hardwareConcurrency: parseInt(hw1) || 0,
    timezone: tz || '',
    plugins: [],
    canvas: '', // 无法从哈希还原canvas，验证时将直接比较哈希值
  };
};

export const generateFingerprint = async (
  options: FingerprintOptions = {},
): Promise<string | null> => {
  const opts = { ...DEFAULT_OPTIONS, ...options };

  try {
    if (opts.debug) {
      console.group('🔍 开始生成指纹');
    }

    // 检查跨域缓存
    const crossDomainCache = localStorage.getItem('cross_domain_fp');
    if (crossDomainCache) {
      try {
        const cacheData = JSON.parse(crossDomainCache) as CacheData;
        const cacheAge = Date.now() - cacheData.timestamp;

        if (cacheAge < opts.ttl! * 1000) {
          if (opts.debug) {
            console.log('✅ 使用跨域缓存指纹', {
              hash: cacheData.fp,
              age: `${Math.round(cacheAge / 1000)}s`,
              data: cacheData.data,
            });
            console.groupEnd();
          }
          return cacheData.fp;
        }
      } catch (e) {
        localStorage.removeItem('cross_domain_fp');
      }
    }

    // 获取基础指纹
    const fp = await FingerprintJS.load();
    const { visitorId } = await fp.get();

    // 收集指纹数据
    const fingerprintData: FingerprintData = {
      visitorId,
      timestamp: Date.now(),
      userAgent: normalizeUserAgent(navigator.userAgent),
      language: navigator.language,
      colorDepth: window.screen.colorDepth,
      deviceMemory: (navigator as any).deviceMemory || 0,
      hardwareConcurrency: navigator.hardwareConcurrency,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      plugins: [], // 不再使用插件信息
      canvas: getCanvasFingerprint(),
    };

    // 创建紧凑格式的指纹
    const compactData = createCompactFingerprint(fingerprintData);
    const hash = btoa(JSON.stringify(compactData));

    // 缓存完整数据
    const cacheData: CacheData = {
      fp: hash,
      timestamp: Date.now(),
      data: fingerprintData,
    };

    // 同时存储到本地缓存和跨域缓存
    localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
    localStorage.setItem('cross_domain_fp', JSON.stringify(cacheData));

    // 存储指纹映射关系，便于以后验证
    localStorage.setItem(`${FP_MAP_PREFIX}${hash}`, JSON.stringify(fingerprintData));

    if (opts.debug) {
      console.log('✨ 指纹生成成功', {
        hash,
        compactData,
        fullData: fingerprintData,
      });
      console.groupEnd();
    }

    return hash;
  } catch (err) {
    if (opts.debug) {
      console.error('❌ 指纹生成失败', err);
      console.groupEnd();
    }
    return null;
  }
};

export const validateFingerprint = async (
  urlFp: string,
  options: FingerprintOptions = {},
): Promise<{
  isValid: boolean;
  similarity?: number;
}> => {
  if (!urlFp) {
    return { isValid: true }; // 如果没有提供指纹，默认为有效
  }

  const opts = { ...DEFAULT_OPTIONS, ...options };

  try {
    if (opts.debug) {
      console.group('🔍 开始验证指纹');
    }

    // 生成当前设备指纹
    const currentFp = await generateFingerprint(opts);

    if (!currentFp) {
      throw new Error('无法生成指纹');
    }

    if (opts.debug) {
      console.log('指纹信息', { currentFp, urlFp });
    }

    // 如果指纹完全匹配
    if (currentFp === urlFp) {
      if (opts.debug) {
        console.log('✅ 指纹完全匹配');
        console.groupEnd();
      }
      return { isValid: true, similarity: 1 };
    }

    // 获取当前设备的指纹数据
    const currentCache = localStorage.getItem(CACHE_KEY);
    if (!currentCache) {
      return { isValid: false, similarity: 0 };
    }

    const currentData = JSON.parse(currentCache).data as FingerprintData;
    const currentCompact = createCompactFingerprint(currentData);

    try {
      // 解码并解析URL中的紧凑指纹数据
      const urlCompactData = JSON.parse(atob(urlFp)) as CompactFingerprint;

      // 尝试获取完整的指纹数据
      let urlFullData: FingerprintData;
      const storedData = localStorage.getItem(`${FP_MAP_PREFIX}${urlFp}`);

      if (storedData) {
        // 如果在本地找到完整数据，使用它
        urlFullData = JSON.parse(storedData);
      } else {
        // 否则从紧凑格式扩展
        urlFullData = expandCompactFingerprint(urlCompactData);
      }

      // 计算基础相似度
      let similarity = 0;

      // 计算关键属性匹配得分
      if (currentData.visitorId === urlFullData.visitorId) similarity += WEIGHTS.visitorId;

      if (currentData.hardwareConcurrency === urlFullData.hardwareConcurrency)
        similarity += WEIGHTS.hardwareConcurrency;

      if (currentData.deviceMemory === urlFullData.deviceMemory) similarity += WEIGHTS.deviceMemory;

      if (currentData.colorDepth === urlFullData.colorDepth) similarity += WEIGHTS.colorDepth;

      if (currentData.language === urlFullData.language) similarity += WEIGHTS.language;

      if (currentData.timezone === urlFullData.timezone) similarity += WEIGHTS.timezone;

      // Canvas 特殊处理，比较哈希值
      if (currentCompact.cv === urlCompactData.cv) similarity += WEIGHTS.canvas;

      // UserAgent 和 Plugins 如果没有完整数据，给予部分分数
      if (!storedData) {
        similarity += WEIGHTS.userAgent * 0.5; // 假设 50% 匹配率
        similarity += WEIGHTS.plugins * 0.5; // 假设 50% 匹配率
      } else {
        // 有完整数据则正常比较
        const ua1 = normalizeUserAgent(currentData.userAgent);
        const ua2 = normalizeUserAgent(urlFullData.userAgent);
        if (ua1 === ua2) similarity += WEIGHTS.userAgent;

        const plugins1 = filterPlugins(currentData.plugins);
        const plugins2 = filterPlugins(urlFullData.plugins);
        const pluginsSimilarity =
          plugins1.filter((p) => plugins2.includes(p)).length /
          Math.max(plugins1.length, plugins2.length);
        similarity += pluginsSimilarity * WEIGHTS.plugins;
      }

      const isValid = similarity >= opts.threshold!;

      if (opts.debug) {
        console.log('📊 指纹相似度:', {
          similarity: `${(similarity * 100).toFixed(2)}%`,
          threshold: `${(opts.threshold! * 100).toFixed(2)}%`,
          result: isValid ? '✅ 通过' : '❌ 未通过',
        });
        console.groupEnd();
      }

      return { isValid, similarity };
    } catch (e) {
      if (opts.debug) {
        console.error('❌ URL指纹解析失败', e);
        console.groupEnd();
      }
      return { isValid: false, similarity: 0 };
    }
  } catch (err) {
    if (opts.debug) {
      console.error('❌ 验证过程出错', err);
      console.groupEnd();
    }
    return { isValid: false };
  }
};

// 获取完整的指纹数据（用于高级应用）
export const getFullFingerprintData = async (
  options: FingerprintOptions = {},
): Promise<FingerprintData | null> => {
  const opts = { ...DEFAULT_OPTIONS, ...options };

  try {
    const fp = await generateFingerprint(opts);
    if (!fp) return null;

    const cached = localStorage.getItem(CACHE_KEY);
    if (!cached) return null;

    return JSON.parse(cached).data as FingerprintData;
  } catch (err) {
    return null;
  }
};

export const utils = {
  normalizeUserAgent,
  filterPlugins,
  calculateSimilarity,
  createCompactFingerprint,
  expandCompactFingerprint,
};
