/**
 * 默认的题目列表请求参数
 * @returns 默认的题目列表请求参数
 */
export const defaultQuestionListRequestParams = (): API.QuestionListRequestParams => {
  return {
    /**
     * 基础树
     */
    baseTreeNodeIds: [],
    /**
     * 业务树
     */
    bizTreeNodeIds: [],
    keyword: '',
    /**
     * 第几页
     */
    page: 1,
    /**
     * 每页数量
     */
    pageSize: 10,
    phaseList: [],
    /**
     * 难度
     */
    questionDifficult: [],
    /**
     * 题目来源
     */
    questionSource: [],
    /**
     * 类型
     */
    questionType: [],
    /**
     * label 题型
     */
    labelQuestionType: [],
    /**
     * 年份
     */
    questionYears: [],
    /**
     * 场景题库
     */
    sceneCategory: [],
    /**
     * 排序字段，createTime, useCount
     */
    sort: 'createTime',
    subjectList: [],
    questionIds: [],
  };
};

/**
 * 获取树中从第一层到指定层级的所有节点的key
 * @param tree 树节点数组
 * @param targetLevel 目标层级（从1开始）
 * @returns 从第一层到目标层级的所有节点key数组
 */
export function getTreeKeysToLevel(
  tree: API.Common.BaseTreeNodeAntdType[],
  targetLevel: number = 1,
): number[] {
  if (!tree || tree.length === 0 || targetLevel < 1) {
    return [];
  }

  const keys: number[] = [];

  function traverse(node: API.Common.BaseTreeNodeAntdType, currentLevel: number) {
    if (currentLevel <= targetLevel) {
      keys.push(node.key);
    }

    if (node.children && currentLevel < targetLevel) {
      node.children.forEach((child) => traverse(child, currentLevel + 1));
    }
  }

  tree.forEach((node) => traverse(node, 1));

  return keys;
}

export function getQuestionListInitParams() {
  return {
    subjectPhase: {
      // 学科学段
      subjectEnum: undefined,
      phaseEnum: undefined,
    },
    baseTreeNodeIds: [], // 基础树
    bizTreeNodeIds: [], // 业务树
    search: {
      // 搜索框
      field: '', // 搜索字段
      keyword: '', // 搜索内容
    },
    filters: {
      // 过滤项
      questionTypeEnums: [], // 类型
      questionDifficultEnums: [], // 难度
      questionYears: [], // 年份
      questionSourceEnums: [], // 来源
      sceneCategoryEnums: [], // 场景题库
    },
    sort: {
      // 排序
      field: 'createTime', // 创建时间
      order: 'desc', // asc, desc
    },
  };
}

interface TreeNodeObjects {
  treeDataList: API.Common.BaseTreeNodeAntdType[];
  treeDataLeafList: API.Common.BaseTreeNodeAntdType[];
  parentNodeList: API.Common.BaseTreeNodeAntdType[];
  topLevelNodes: API.Common.BaseTreeNodeAntdType[];
  treeDataMap: Record<number, API.Common.BaseTreeNodeAntdType>;
}

export function getTreeNodeObjects(treeData: API.Common.BaseTreeNodeAntdType[]): TreeNodeObjects {
  const treeDataList: API.Common.BaseTreeNodeAntdType[] = [];
  const treeDataMap: Record<number, API.Common.BaseTreeNodeAntdType> = {};
  const treeDataLeafList: API.Common.BaseTreeNodeAntdType[] = [];
  const topLevelNodes: API.Common.BaseTreeNodeAntdType[] = [];
  const parentNodeList: API.Common.BaseTreeNodeAntdType[] = [];

  // 遍历树节点生成树数据列表和叶子节点列表
  function traverse(node: API.Common.BaseTreeNodeAntdType) {
    if (treeDataMap[node.key]) {
      return;
    }
    treeDataMap[node.key] = node;
    treeDataList.push(node);
    if (!(node.children && node.children.length > 0)) {
      treeDataLeafList.push(node);
    }
    if (node.children && node.children.length > 0) {
      parentNodeList.push(node);
      node.children.forEach((child) => {
        traverse(child);
      });
    }
  }
  treeData.forEach((node) => {
    topLevelNodes.push(node);
    traverse(node);
  });

  return {
    treeDataList,
    treeDataLeafList,
    parentNodeList,
    treeDataMap,
    topLevelNodes,
  };
}

export function getTreeDataByFilter(
  treeData: API.Common.BaseTreeNodeAntdType[],
  filter: API.Common.TreeNodeFilterType,
  isOnlyFilterLeafNode: boolean = false,
  rootNodeKey: number | undefined = undefined,
): API.Common.BaseTreeNodeAntdType[] {
  if (!treeData || treeData.length === 0) {
    return [];
  }

  const { treeDataList, treeDataLeafList, treeDataMap } = getTreeNodeObjects(treeData);

  // 过滤树节点
  function filterFn(nodes: API.Common.BaseTreeNodeAntdType[]): API.Common.BaseTreeNodeAntdType[] {
    return nodes.filter((node) => {
      const { key, value, isEqual } = filter;
      const nodeValue = node[key as keyof API.Common.BaseTreeNodeAntdType];
      if (nodeValue !== undefined) {
        if (!isEqual) {
          return String(nodeValue).includes(String(value));
        }
        return nodeValue === value;
      }
      return false;
    });
  }

  // 返回需要的顶层节点
  function getTopLevelNodes(
    nodes: API.Common.BaseTreeNodeAntdType[],
  ): API.Common.BaseTreeNodeAntdType[] {
    return nodes.filter((node) => {
      const { parentsKey } = node;
      if (rootNodeKey && parentsKey && parentsKey.length === 1) {
        return parentsKey[0] === rootNodeKey;
      }
      return !parentsKey || parentsKey.length === 0;
    });
  }

  // 只过滤叶子节点
  if (isOnlyFilterLeafNode) {
    const filteredTreeDataList = filterFn(treeDataLeafList);
    const keys = filteredTreeDataList.map((node) => node.key);
    const unNeddNodes = treeDataLeafList.filter((node) => !keys.includes(node.key));
    unNeddNodes.forEach((node) => {
      const { parentsKey } = node;
      if (parentsKey && parentsKey.length > 0) {
        const parentNode = treeDataMap[parentsKey.at(-1) as number];
        if (parentNode) {
          parentNode.children = parentNode.children?.filter((child) => child.key !== node.key);
        }
      }
      if (parentsKey && parentsKey.length === 0) {
        treeDataList.splice(treeDataList.indexOf(node), 1);
      }
    });

    return getTopLevelNodes(treeDataList);
  }

  // 过滤所有节点
  const filteredTreeDataList = filterFn(treeDataList);
  console.log('filteredTreeDataList ====》: ', filteredTreeDataList);
  const needTreeDataListKeys = filteredTreeDataList.reduce((acc, node) => {
    const { parentsKey } = node;
    acc.push(node.key);
    // if (parentsKey && parentsKey.length > 0) {
    //   return [...acc, ...parentsKey];
    // }
    const keys = parentsKey && parentsKey.length > 0 ? [...acc, ...parentsKey] : acc;
    const result = [...new Set(keys)];
    console.log('result ====》: ', result);
    return result;
  }, [] as number[]);
  console.log('needTreeDataListKeys ====》: ', needTreeDataListKeys);
  const deleteTreeDataList = treeDataList.filter(
    (node) => !needTreeDataListKeys.includes(node.key),
  );
  deleteTreeDataList.forEach((node) => {
    const { parentsKey } = node;
    if (parentsKey && parentsKey.length > 0) {
      const parentNode = treeDataMap[parentsKey.at(-1) as number];
      if (parentNode) {
        parentNode.children = parentNode.children?.filter((child) => child.key !== node.key);
      }
    }
    if (parentsKey && parentsKey.length === 0) {
      treeDataList.splice(treeDataList.indexOf(node), 1);
    }
    // 如果顶级节点有父级根节点，则也需要移除
    if (rootNodeKey && parentsKey && parentsKey.length === 1 && parentsKey[0] === rootNodeKey) {
      treeDataList.splice(treeDataList.indexOf(node), 1);
    }
  });
  console.log('treeDataList ====》: ', treeDataList);
  return getTopLevelNodes(treeDataList);
}

export function getBaseTreeNodeMap(treeData: API.BaseTreeNode): Map<number, API.BaseTreeNode> {
  const treeDataMap = new Map<number, API.BaseTreeNode>();

  // 遍历树节点生成树数据列表和叶子节点列表
  function traverse(node: API.BaseTreeNode) {
    treeDataMap.set(node.baseTreeNodeId, node);
    if (node.baseTreeNodeChildren && node.baseTreeNodeChildren.length > 0) {
      treeDataMap.set(node.baseTreeNodeId, node);
      node.baseTreeNodeChildren.forEach((child: API.BaseTreeNode) => {
        traverse(child);
      });
    }
  }
  traverse(treeData);

  return treeDataMap;
}
