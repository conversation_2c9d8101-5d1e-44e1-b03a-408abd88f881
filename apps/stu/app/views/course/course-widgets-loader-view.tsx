"use client";

// import ScrollCorner from "@/public/images/corner.svg";
import { DialogView } from "@/app/components/dialog/default-dialog";
import { useCourseWidgetModel } from "@/app/models/course-widget-model";

import { Loading } from "@/app/components/common/loading";
import { ErrorPage } from "@/app/components/error-page";
import { CourseWidgetSummaryWithoutStatus } from "@/types/app/course";
import { useComputed } from "@preact-signals/safe-react";
import { cn } from "@repo/ui/lib/utils";
import {
  ComponentProps,
  FC,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
} from "react";
import { ExerciseInCourseView } from "../exercise-in-course";
import { GuideWidgetView } from "../guide/guide-view";
import { InteractiveView } from "../interactive/interactive-view";
import { VideoWidgetView } from "../video/video-view";
import { useCourseViewContext } from "./course-view-context";
import { WidgetFlipAnimation } from "./widget-flip-animation";

const WidgetLoader: FC<
  {
    summary: CourseWidgetSummaryWithoutStatus;
  } & ComponentProps<"div">
> = ({ summary }) => {
  const {
    total,
    next,
    reportCostTime,
    knowledgeId,
    lessonId,
    nextQuestionParams,
    exerciseCompletedRecord,
    refWidgets,
    currentIndex,
  } = useCourseViewContext();
  const ref = useRef<HTMLDivElement | null>(null);
  const { name, index } = summary;
  const isActive = useComputed(() => {
    return index === currentIndex.value;
  });

  const shouldLoad = useComputed(() => {
    return (
      index === currentIndex.value ||
      index === currentIndex.value + 1 ||
      index === currentIndex.value - 1
    );
  });

  const { data, isLoading, error, refresh } = useCourseWidgetModel({
    knowledgeId,
    lessonId,
    summary: shouldLoad.value ? summary : undefined,
    nextQuestionParams,
  });

  useEffect(() => {
    if (ref.current) {
      const arr = [...refWidgets.value];
      arr[index] = ref.current;
      refWidgets.value = arr;
    }
  }, [ref, index, refWidgets, isLoading]);

  const handleComplete = useCallback(
    (totalTimeSpent?: number) => {
      if (totalTimeSpent) {
        reportCostTime(totalTimeSpent);
      }

      if (!exerciseCompletedRecord.value?.[data?.index || 0]) {
        const newExerciseCompleted = {
          ...exerciseCompletedRecord.value,
          [data?.index || 0]: true,
        };
        exerciseCompletedRecord.value = newExerciseCompleted;
      }

      console.log("handleComplete", {
        totalTimeSpent,
        data,
        exerciseCompleted: exerciseCompletedRecord.value,
      });

      next();
    },
    [next, reportCostTime, data, exerciseCompletedRecord]
  );

  const widget = useMemo(() => {
    if (isLoading || !shouldLoad.value) {
      return <Loading />;
    }

    if (error || !data) {
      return <ErrorPage error={error} onRefresh={refresh} />;
    }

    const { type } = data;
    if (type === "guide") {
      return (
        <GuideWidgetView
          content={data}
          active={isActive.value}
          totalGuideCount={total}
        />
      );
    }

    if (type === "exercise") {
      console.log("exerciseData123", {
        data,
        exerciseCompletedRecord: exerciseCompletedRecord.value,
      });

      // 获取练习数据
      let exerciseData = data;
      if (exerciseCompletedRecord.value?.[data.index]) {
        exerciseData = {
          ...exerciseData,
          data: {
            ...exerciseData.data,
            hasNextQuestion: false,
          },
        };
      }

      return (
        <ExerciseInCourseView
          activeInCourse={isActive.value}
          widgetIndex={data.index}
          exerciseData={exerciseData}
          onComplete={handleComplete}
        />
      );
    }

    if (type === "interactive") {
      const interactiveData = data;
      return (
        <InteractiveView
          active={isActive.value}
          index={data.index}
          url={interactiveData.data.url}
          type={interactiveData.data.typeName}
          onReport={(e) => {
            console.log(e);
          }}
        />
      );
    }

    if (type === "video") {
      return (
        <VideoWidgetView
          content={data}
          active={isActive.value}
          totalGuideCount={total}
        />
      );
    }

    return (
      <div className="course-widget-unsupported">不支持的组件类型: {type}</div>
    );
  }, [
    isLoading,
    data,
    error,
    refresh,
    isActive.value,
    total,
    exerciseCompletedRecord,
    handleComplete,
    shouldLoad,
  ]);

  return (
    <div
      ref={ref}
      data-name={`widget-${index}-${name}`}
      className={cn(
        "bg-bg-guide relative h-screen w-full overflow-y-auto overscroll-none",
        summary.type === "exercise" && "transform-gpu"
      )}
    >
      {widget}
      <div className="h-[1px] w-full bg-transparent" />
    </div>
  );
};

const Widgets = memo(function Widgets({
  widgetList,
}: {
  widgetList: CourseWidgetSummaryWithoutStatus[];
}) {
  if (widgetList.length === 0) {
    return null;
  }
  return (
    <>
      {widgetList.map((it) => {
        const { index } = it;
        return <WidgetLoader key={`widget-${index}`} summary={it} />;
      })}
    </>
  );
});

const CourseWidgetsLoaderView: FC<ComponentProps<"div">> = () => {
  const {
    isVersionChanged,
    isLoading,
    error,
    widgetList,
    showFlipAnimation,
    flipDirection,
    flipTargetWidgetType,
    completeFlipAnimation,
    refCourseContainer,
    exit,
  } = useCourseViewContext();

  if (isLoading) {
    return <Loading />;
  }

  if (error) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <p className="text-red-1 text-center text-sm">{error.message}</p>
      </div>
    );
  }

  return (
    <>
      <WidgetFlipAnimation
        show={showFlipAnimation}
        direction={flipDirection}
        flipTargetWidgetType={flipTargetWidgetType}
        onAnimationComplete={completeFlipAnimation}
      />
      <div
        ref={refCourseContainer}
        className="relative h-screen w-full transform-gpu overflow-y-scroll"
      >
        <DialogView
          title="当前课程内容已更新，请退出后重新进入课程"
          open={isVersionChanged.value}
          buttons={[
            {
              text: "确定",
              onClick: exit,
              color: "red",
            },
          ]}
        ></DialogView>
        <Widgets widgetList={widgetList} />
      </div>
    </>
  );
};

export { CourseWidgetsLoaderView };
