import { DictTypeEnum } from '@/services/dict';
import { useModel } from '@umijs/max';
import { Tooltip } from 'antd';
import { ReactNode } from 'react';
import { TeacherInfo, TeacherJobInfo } from '../type';
import { JobType, jobTypeOptions, useTeacherManagement } from './useTeacherManagement';
export const useRenderJonInfos = () => {
  const { dictCache } = useModel('dictModels');
  const { gradeOnlyTreeData, treeData } = useTeacherManagement();
  // 从树数据中获取年级名称
  const getGradeName = (gradeId: number): string => {
    if (!gradeId) return '';

    // 先从年级树数据中查找
    const gradeNode = gradeOnlyTreeData?.find((node) => node.gradeId === gradeId);
    if (gradeNode?.title) return gradeNode.title as string;

    // 如果在树中找不到，再尝试从完整树数据中查找
    for (const node of treeData || []) {
      if (node.gradeId === gradeId) {
        return node.title as string;
      }
    }

    return `年级${gradeId}`;
  };

  // 从树数据中获取班级名称
  const getClassName = (classId: number, gradeId?: number): string => {
    if (!classId) return '';

    // 遍历树数据查找班级节点
    for (const gradeNode of treeData || []) {
      if (gradeId && gradeNode.gradeId !== gradeId) continue;

      if (gradeNode.children) {
        for (const groupNode of gradeNode.children) {
          if (groupNode.children) {
            const classNode = groupNode.children.find((node) => node.classId === classId);
            if (classNode) {
              // 提取班级名称，不需要包含年级和类型信息
              // const fullTitle = classNode.title as string;
              // 假设格式为"高一 1班（真实）"，提取"1班"部分
              // const match = fullTitle.match(/\s(.+?)（/);
              // return match ? match[1] : fullTitle;
              return classNode.className || '';
            }
          }
        }
      }
    }

    return `班级${classId}`;
  };

  // 获取学科名称
  const getSubjectName = (subjectId: number | string): string => {
    if (!subjectId) return '';

    // 从数据字典中获取学科名称
    const subject = dictCache?.[DictTypeEnum.SUBJECT]?.find(
      (s: { meta_dict_key: string | number; meta_dict_name: string }) =>
        Number(s.meta_dict_key) === Number(subjectId),
    );

    return subject?.meta_dict_name || `学科${subjectId}`;
  };

  // 获取当前年级的班级选项
  const getClassOptions = (gradeId?: number) => {
    if (!gradeId) return [];

    const options: { label: string; value: number }[] = [];
    const grade = treeData?.find((node) => node.gradeId === gradeId);

    if (grade?.children) {
      grade.children.forEach((group) => {
        if (group.children) {
          group.children.forEach((classNode) => {
            if (classNode.classId) {
              // 从完整名称中提取班级名称并添加类型标注
              const fullTitle = classNode.title as string;
              const match = fullTitle.match(/\s(.+?)（(.+?)）/);
              const className = match ? `${match[1]}（${match[2]}）` : fullTitle;

              options.push({
                label: className,
                value: classNode.classId,
              });
            }
          });
        }
      });
    }

    return options;
  };

  // 格式化单个职务信息
  const formatJobInfo = (jobInfo: TeacherJobInfo): string => {
    if (!jobInfo) return '';

    const { jobType, jobInfos, jobSubject } = jobInfo;
    console.log('jobSubject', jobSubject);

    // 获取职务类型名称
    const jobTypeName = jobTypeOptions.find((option) => option.value === jobType)?.label || '';
    const subjectName = getSubjectName(jobSubject);
    // 根据不同职务类型格式化信息
    switch (jobType) {
      case JobType.PRINCIPAL: // 校长
        return jobTypeName;

      case JobType.GRADE_DIRECTOR: // 年级主任
        if (jobInfos && jobInfos.length > 0) {
          const grades = jobInfos
            .map((info) => getGradeName(info.jobGrade))
            .filter(Boolean)
            .join('、');
          return `${jobTypeName}：${grades ? `${grades}` : ''}`;
        }
        return jobTypeName;

      case JobType.SUBJECT_LEADER: {
        // 学科组长
        const subjectLeaderName = getSubjectName(jobSubject);

        if (jobInfos && jobInfos.length > 0) {
          const grades = jobInfos
            .map((info) => getGradeName(info.jobGrade))
            .filter(Boolean)
            .join('、');
          return `${subjectLeaderName}-${jobTypeName}：${grades ? `${grades}` : ''}`;
        }
        return `${subjectLeaderName}：${jobTypeName}`;
      }

      case JobType.SUBJECT_TEACHER: // 学科教师
        if (jobInfos && jobInfos.length > 0) {
          const classDetails = jobInfos
            .map((info) => {
              const gradeName = getGradeName(info.jobGrade);
              if (!gradeName) return '';

              if (info.jobClass && info.jobClass.length > 0) {
                const classes = info.jobClass
                  .map((classId) => getClassName(classId, info.jobGrade))
                  .filter(Boolean)
                  .join('、');
                return `${gradeName}：${classes}`;
              }
              return gradeName;
            })
            .filter(Boolean)
            .join('、');

          return `${subjectName}${jobTypeName}${classDetails ? ` - ${classDetails}` : ''}`;
        }
        return `${subjectName}${jobTypeName}`;

      case JobType.CLASS_TEACHER: // 班主任
        if (jobInfos && jobInfos.length > 0) {
          const classDetails = jobInfos
            .map((info) => {
              const gradeName = getGradeName(info.jobGrade);
              if (!gradeName) return '';

              if (info.jobClass && info.jobClass.length > 0) {
                const classes = info.jobClass
                  .map((classId) => getClassName(classId, info.jobGrade))
                  .filter(Boolean)
                  .join('、');
                return `${gradeName}：${classes}`;
              }
              return gradeName;
            })
            .filter(Boolean)
            .join('、');

          return `${jobTypeName}${classDetails ? `(${classDetails})` : ''}`;
        }
        return jobTypeName;

      default:
        return jobTypeName;
    }
  };

  // 渲染职务信息列
  const renderJobInfos = (_: ReactNode, record: TeacherInfo) => {
    const jobInfos = record.teacherJobInfos;
    if (!jobInfos || jobInfos.length === 0) {
      return '-';
    }

    // 将每个职务格式化后组合成一个字符串，用分号分隔
    const formattedInfos = jobInfos.map((jobInfo) => formatJobInfo(jobInfo)).filter(Boolean);
    const displayText = formattedInfos.join('；');

    // 每个职务单独一行，用于悬浮提示
    const tooltipContent = (
      <div className="job-info-tooltip">
        {formattedInfos.map((info, index) => (
          <div key={index} className="mb-2 whitespace-pre-wrap">
            {info}
          </div>
        ))}
      </div>
    );

    return (
      <Tooltip title={tooltipContent} placement="topLeft">
        <div className="job-info-cell">{displayText || '-'}</div>
      </Tooltip>
    );
  };
  return {
    getGradeName,
    getClassName,
    getSubjectName,
    getClassOptions,
    formatJobInfo,
    renderJobInfos,
  };
};
